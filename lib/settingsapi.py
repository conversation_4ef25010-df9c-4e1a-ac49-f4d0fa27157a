import boto3
import json
import os
import datetime
from typing import Dict, Type, Tuple, Any
from pydantic_settings import BaseSettings, PydanticBaseSettingsSource

SECRETS_CACHE_TIMEOUT_SECS = 5

stack_name = os.environ.get('STACK', 'dev')
if os.environ.get('AWS_LAMBDA_FUNCTION_NAME') is None and stack_name in {'prod'}:
    if os.environ.get('PROD_IS_OK') != 'YES':
        check = input('Are you sure you mean to run against prod? (type YES if you are) ')
        if check.strip() != 'YES':
            print("Aborting")
            exit(1)


class AWSSecretsManagerSettingSource(PydanticBaseSettingsSource):

    def get_field_value(self, name: str) -> Tuple[str, Any]:
        if self._secrets_cache is None or (datetime.datetime.now() - self._secrets_cache_stamp > datetime.timedelta(seconds=SECRETS_CACHE_TIMEOUT_SECS)):
            session = boto3.session.Session()
            client = session.client(service_name='secretsmanager', region_name=os.getenv('AWS_REGION', 'eu-west-1'))

            secret_name = os.getenv('AWS_SECRETS_NAME', f'crc-backend-secrets-eu-west-1-{stack_name}')
            response = client.get_secret_value(SecretId=secret_name)
            self._secrets_cache = json.loads(response['SecretString'])
            self._secrets_cache_stamp = datetime.datetime.now()

        return name, self._secrets_cache.get(name, None)

    def __call__(self) -> dict[str, Any]:
        self._secrets_cache = None
        self._secrets_cache_stamp = None

        secrets: Dict[str, Any] = {}

        for field_name, _ in self.settings_cls.model_fields.items():
            key, value = self.get_field_value(field_name)

            if value is not None:
                secrets[key] = value

        return secrets


class Settings(BaseSettings):
    env: str = stack_name
    # sendgrid
    SENDGRID_API_KEY: str = ''
    SENDGRID_FROM_EMAIL: str = ''
    SENDGRID_API_RETURN_LIMIT: int = 1000
    SENDGRID_DATE_RANGE_OFFSET: int = 15
    # sendgrid email event tracking
    TRACKED_TEMPLATES: str = ''
    # this can be set on non-prod environments to prevent the tracking of test emails by prod
    TRACKED_TEMPLATE_SUFFIX: str = ''
    # portaldb
    PORTALDB_URL: str = ''
    # salesforce
    SF_INSTANCE: str = ''
    SF_CLIENT_ID: str = ''
    SF_CLIENT_SECRET: str = ''
    # s3
    SF_DATA_BUCKET: str = ''
    SF_CSV_BUCKET: str = ''
    SF_BANNERS_BUCKET: str = ''
    SF_SIGNATURES_BUCKET: str = ''
    SF_ADH_BUCKET: str = ''  # bucket for transfer into ADH
    SF_SRP_REPORTS_BUCKET: str = ''
    SG_TEMPLATE_CACHE_BUCKET: str = ''
    # amplify
    survey_amplify_app_id: str = ''
    survey_amplify_domain: str = ''
    # apis
    salesforce_auth_key: str = ''
    api_survey_auth_key: str = ''
    csv_download_auth_key: str = ''
    # sqs queue names
    survey_round_scheduler_queue_url: str = ''
    sync_customer_surveys_queue_url: str = ''
    sync_survey_clients_queue_url: str = ''
    sync_optout_to_sf_queue_url: str = ''
    send_email_to_sg_queue_url: str = ''
    send_email_to_ses_queue_url: str = ''
    # ses
    ses_access_key: str = ''
    ses_secret_access_key: str = ''
    ses_configuration_set_name: str = ''
    # auth stuff
    confirmation_code_encryption_key: str = ''
    cognito_key_ids_arn: str = ''
    verify_login_link: str = ''
    forgotten_password_link: str = ''
    cognito_client_id: str = ''
    cognito_client_secret: str = ''
    cognito_issuer: str = ''
    cognito_audience: str = ''
    cognito_keys_url: str = ''
    cognito_domain: str = ''
    cognito_redirect_uri: str = ''
    cognito_user_pool_id: str = ''
    # monitoring
    sns_slack_webhook_url: str = ''
    sns_slack_resolution_error_webhook_url: str = ''
    # sentry
    sentry_environment: str = ''
    sentry_dsn: str = ''
    sentry_dsn_survey: str = ''
    # links
    survey_ui_link: str = ''
    client_area_ui_link: str = ''
    # monitoring settings
    monitordb_timeseries_url: str = ''
    monitordb_email_url: str = ''
    monitordb_salesforce_url: str = ''
    monitor_email_tenant_id: str = ''
    monitor_email_client_id: str = ''
    monitor_email_client_secret: str = ''
    monitor_assets_bucket: str = ''

    @classmethod
    def settings_customise_sources(
        cls,
        settings_cls: Type[BaseSettings],
        init_settings: PydanticBaseSettingsSource,
        env_settings: PydanticBaseSettingsSource,
        dotenv_settings: PydanticBaseSettingsSource,
        file_secret_settings: PydanticBaseSettingsSource,
    ) -> Tuple[PydanticBaseSettingsSource, ...]:
        return (
            init_settings,
            env_settings,
            dotenv_settings,
            file_secret_settings,
            AWSSecretsManagerSettingSource(settings_cls),
        )
