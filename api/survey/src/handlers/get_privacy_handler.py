from bson import ObjectId
from litestar.exceptions import NotFoundException
from lib.portaldbapi import DocumentDB


class GetPrivacyHandler:
    def __init__(self, code: str, db: DocumentDB):
        self.code = code
        self.db = db

    def handler(self):
        query = dict(
            _id=ObjectId(self.code),
        )
        survey = self.db.surveys.find_one(query)
        
        if not survey:
            raise NotFoundException()

        return {
            "name": survey.get('client_name'),
        }