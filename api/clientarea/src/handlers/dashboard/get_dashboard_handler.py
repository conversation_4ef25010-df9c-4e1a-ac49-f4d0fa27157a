import re
import time
import asyncio
from typing import Dict
from datetime import datetime, date
from importlib import import_module
from litestar.exceptions import PermissionDeniedException
from lib.portaldbapi import DocumentDBAsync
from api.clientarea.src.dependencies import User


async def fetch_responses(collection, query, projection):
    records = []
    cursor = collection.find(query, projection).batch_size(2500)
    async for record in cursor:
        records.append(record)
    return records


def build_response_query(reporting_views, global_filters, user: User, round_index_key:str|None = None) -> Dict:
    # build the base query for the dashboard cards (hard filters)
    base_query = {'$or': []}
    key_accounts_filter = None
    if global_filters['key_account']:
        if 'true' in global_filters['key_account']:
            key_accounts_filter = True
        elif 'false' in global_filters['key_account']:
            key_accounts_filter = False

    for _, reporting_view in reporting_views.items():
        sub_query = {
            '$and': [
                {'client_id': {'$in': reporting_view['clients']}},
                {'account_id': {'$in': reporting_view['accounts']}},
                {'market_id': {'$in': reporting_view['markets']}},
            ]
        }
        if key_accounts_filter is not None:
            operator = '$in' if key_accounts_filter else '$nin'
            sub_query['$and'].append({'account_id': {operator: reporting_view['key_accounts']}})

        if reporting_view['customers']:
            sub_query['$and'].append({
                '$or': [
                    {'agency_brand_id': {'$in': reporting_view['customers']}},
                    {'agency_brand_sub_1_id': {'$in': reporting_view['customers']}}
                ]
            })
        if reporting_view.get('teams', []) and 'dashboard_key_teams' in user.features:
            sub_query['$and'].append({
                'team_id': {'$in': reporting_view['teams']},
            })
        if reporting_view['type'] in ['Market', 'Regional']:
            sub_query['$and'].append({
                '$or': [
                    {'team_market_id': None},
                    {'team_market_id': {'$in': reporting_view['markets']}}
                ]
            })
        base_query['$or'].append(sub_query)

    if user.team_id:
        base_query['team_id'] = user.team_id

    if user.round_index_limit and round_index_key:
        base_query[round_index_key] = {'$in': user.round_index_limit}
    
    # if no base query generated, bail
    if len(base_query['$or']) == 0:
        raise PermissionDeniedException('No query generated from reporting views for user')
    
    return base_query


def get_response_projection(base_projection_fields: set, dashboard_cards: list, round_index_key: str) -> dict:
    # get the projection fields needed by the cards
    # and combine with the base response fields required by the handler
    card_projection = set()
    card_projection.add(round_index_key)
    for card in dashboard_cards:
        if card.get('filters'):
            card_projection.update(card['filters'])
        if card.get('projection'):
            card_projection.update(card['projection'].keys())
        if card.get('query'):
            card_projection.update(card['query'].keys())
    calculated_response_projection_fields = base_projection_fields.union(card_projection)

    response_projection = {field: 1 for field in calculated_response_projection_fields}
    return response_projection


def get_response_data_required(dashboard_config) -> set:
    # get the type of response data this dashboard requires
    # by looking at the cards
    data_required = set()
    for card in dashboard_config.get('cards', []):
        data_required.update(card.get('data_required', ['trr']))
    return data_required


def get_reporting_views(all_reporting_views: dict, barometer: bool) -> dict:
    # return the 
    trr_views = {}
    barometer_views = {}
    result = {}
    for key, reporting_view in all_reporting_views.items():
        if reporting_view.get('survey_type') and reporting_view.get('survey_type') == 'Barometer':
            barometer_views[key] = reporting_view
        else:
            trr_views[key] = reporting_view
    if barometer:
        # if for barometer, return the barometer views if there are any
        # otherwise fall back to the trr views
        result = barometer_views if barometer_views else trr_views
    else:
        result = trr_views
    return result


def get_round_lookup(responses: list, round_index_key: str):
    rounds_lookup = {}
    for response in responses:
        if not response.get('responded'):
            continue
        # FIXME: set date in response agent
        insights_start_date = response.get('insights_start_date')
        round_name = response.get('round_name')
        round_index = response.get(round_index_key)
        if insights_start_date:
            insights_start_date = datetime.strptime(response.get('insights_start_date'), "%Y-%m-%d")
        if round_index == 0:
            round_name = 'Latest Round'
        rounds_lookup[response.get(round_index_key)] = {
            'round_index': round_index,
            'round_name': round_name,
            'insights_date': insights_start_date
        }
    return rounds_lookup


class GetDashboardHandler:
    def __init__(self, dashboard, global_filters, local_filters, user: User, documentdb: DocumentDBAsync):
        self.dashboard_type = dashboard
        self.global_filters = global_filters
        self.local_filters = local_filters
        self.metadata = True
        self.user = user
        self.db = documentdb

        self.card_type_modules_path = 'api.clientarea.src.utils.cards'

        self.dashboard_config_projection = {
            '_id': 0, 
            'title': 1, 
            'description': 1, 
            'info': 1,
            'userFilterable': 1, 
            'showUserInfo': 1, 
            'cards': 1,
        }
        self.base_response_projection_fields = set(
            [
                'Id',
                # required for contact search
                'contact_id',
                'contact_name',
                # required for building filter values
                'agency_brand_id',
                'agency_brand_name',
                'agency_brand_sub_1_id',
                'agency_brand_sub_1_name',
                'account_id',
                'account_name',
                'contact_division',
                'contact_type',
                'market_id',
                'market_name',
                'market_country_id',
                'market_country',
                'market_region_id',
                'market_region',
                'responded',
                'sector',
                'survey_name',
                'team_name',
                'team_type',
                'team_market_id',
                'team_market_name',
                'team_market_group',
                # required for round lookup
                'round_name',
                'insights_start_date',
                # required by base Card implementation
                'is_extra_question',
            ]
        )


    def generate_filters_and_contacts(self, round_index_key: str, rounds_lookup: dict, filtered_round: int, responses: list, build_contacts: bool):
        # create the dashboard filters and list of searchable contacts
        f_themes = {}
        f_agencies = {}
        f_accounts = {}
        f_key_accounts = {'true': 'Yes', 'false': 'No'}
        f_markets = {}
        f_market_countries = {}
        f_market_regions = {}
        f_sectors = {}
        f_surveys = {}
        f_contact_types = {}
        f_contact_divisions = {}
        f_teams = {}
        f_team_types = {}
        f_team_markets = {}
        f_team_market_groups = {}
        f_rounds = {}
        f_contacts = {}
        for d in responses:
            # we only want responses for filters and contacts
            if d.get('responded') is False:
                continue

            if f_contacts.get(d.get('contact_id')) is None and build_contacts:
                if not d.get('contact_id'):
                    # contact may have been deleted from SF and left dangling Panel Members (see PLAT-830)
                    # we have to ignore this contact for now
                    continue
                contact_info = dict(
                    contact_id=d.get('contact_id'),
                    contact_name=d.get('contact_name'),
                    contact_type=d.get('contact_type'),
                    account_name=d.get('account_name'),
                    market_country=d.get('market_country'),
                )
                f_contacts[d['contact_id']] = contact_info

            # we only want the selected round for filters
            if d.get(round_index_key) != filtered_round:
                continue

            theme_categories = d.get('theme_categories', [])
            for category in theme_categories:
                if not f_themes.get(category) and category is not None:
                    category_id = category.replace(' ', '_').lower()
                    f_themes[category_id] = category

            agency_brand_id: str = d.get('agency_brand_id')
            agency_brand_name: str = d.get('agency_brand_name')
            if not f_agencies.get(agency_brand_id) and agency_brand_id is not None:
                f_agencies[agency_brand_id] = agency_brand_name

            agency_brand_sub_1_id: str = d.get('agency_brand_sub_1_id')
            agency_brand_sub_1_name: str = d.get('agency_brand_sub_1_name')
            if not f_agencies.get(agency_brand_sub_1_id) and agency_brand_sub_1_id is not None:
                f_agencies[agency_brand_sub_1_id] = agency_brand_sub_1_name

            account_id: str = d.get('account_id')
            account_name: str = d.get('account_name')
            if not f_accounts.get(account_id) and account_id is not None:
                f_accounts[account_id] = account_name

            market_id: str = d.get('market_id')
            market_name: str = d.get('market_name')
            if not f_markets.get(market_id) and market_id is not None:
                f_markets[market_id] = market_name

            market_country_id: str = d.get('market_country_id')
            market_country_name: str = d.get('market_country')
            if not f_market_countries.get(market_country_id) and market_country_id is not None:
                f_market_countries[market_country_id] = market_country_name
            
            market_region_id: str = d.get('market_region_id')
            market_region_name: str = d.get('market_region')
            if not f_market_regions.get(market_region_id) and market_region_id is not None:
                f_market_regions[market_region_id] = market_region_name

            sector: str = d.get('sector')
            if not f_sectors.get(sector) and sector is not None:
                sector_id = sector.replace(' ', '_').lower()
                f_sectors[sector_id] = sector

            survey_name: str = d.get('survey_name')
            if not f_surveys.get(survey_name) and survey_name is not None:
                survey_name_id = survey_name.replace(' ', '_').lower()
                f_surveys[survey_name_id] = survey_name

            contact_type: str = d.get('contact_type')
            if not f_contact_types.get(contact_type) and contact_type is not None:
                contact_type_id = contact_type.replace(' ', '_').lower()
                f_contact_types[contact_type_id] = contact_type
            
            contact_division: str = d.get('contact_division')
            if not f_contact_divisions.get(contact_division) and contact_division is not None:
                contact_division_id = contact_division.replace(' ', '_').lower()
                f_contact_divisions[contact_division_id] = contact_division
            
            team_name: str = d.get('team_name')
            if not f_teams.get(team_name) and team_name is not None:
                team_name_id = team_name.replace(' ', '_').lower()
                f_teams[team_name_id] = team_name
            
            team_type: str = d.get('team_type')
            if not f_team_types.get(team_type) and team_type is not None:
                team_type_id = team_type.replace(' ', '_').lower()
                f_team_types[team_type_id] = team_type

            team_market_id: str = d.get('team_market_id')
            team_market_name: str = d.get('team_market_name')
            if not f_team_markets.get(team_market_id) and team_market_id is not None:
                f_team_markets[team_market_id] = team_market_name

            team_market_group: str = d.get('team_market_group')
            if not f_team_market_groups.get(team_market_group) and team_market_group is not None:
                team_market_group_id = team_market_group.replace(' ', '_').lower()
                f_team_market_groups[team_market_group_id] = team_market_group
        
        f_rounds = {str(d['round_index']): d['round_name'] for d in rounds_lookup.values()}
                
        # restructure the filters
        themes = [{'label': v, 'value': v} for k, v in f_themes.items()]
        agencies = [{'label': v, 'value': k} for k, v in f_agencies.items()]
        accounts2 = [{'label': v, 'value': k} for k, v in f_accounts.items()]
        key_account = [{'label': v, 'value': k} for k, v in f_key_accounts.items()]
        markets = [{'label': v, 'value': k} for k, v in f_markets.items()]
        countries = [{'label': v, 'value': k} for k, v in f_market_countries.items()]
        regions = [{'label': v, 'value': k} for k, v in f_market_regions.items()]
        sectors = [{'label': v, 'value': v} for k, v in f_sectors.items()]
        surveys = [{'label': v, 'value': k} for k, v in f_surveys.items()]
        contact_types = [{'label': v, 'value': v} for k, v in f_contact_types.items()]
        contact_divisions = [{'label': v, 'value': v} for k, v in f_contact_divisions.items()]
        teams = [{'label': v, 'value': v} for k, v in f_teams.items()]
        team_types = [{'label': v, 'value': v} for k, v in f_team_types.items()]
        teaf_markets = [{'label': v, 'value': k} for k, v in f_team_markets.items()]
        team_market_groups = [{'label': v, 'value': v} for k, v in f_team_market_groups.items()]
        rounds = [{'label': v, 'value': int(k)} for k, v in f_rounds.items()]
        # make contacts a list
        contacts = list(f_contacts.values())

        # sort the filters
        themes = sorted(themes, key=lambda x: x['label'])
        agencies = sorted(agencies, key=lambda x: x['label'])
        accounts2 = sorted(accounts2, key=lambda x: x['label'])
        markets = sorted(markets, key=lambda x: x['label'])
        countries = sorted(countries, key=lambda x: x['label'])
        regions = sorted(regions, key=lambda x: x['label'])
        sectors = sorted(sectors, key=lambda x: x['label'])
        surveys = sorted(surveys, key=lambda x: x['label'])
        contact_types = sorted(contact_types, key=lambda x: x['label'])
        contact_divisions = sorted(contact_divisions, key=lambda x: x['label'])
        teams = sorted(teams, key=lambda x: x['label'])
        team_types = sorted(team_types, key=lambda x: x['label'])
        teaf_markets = sorted(teaf_markets, key=lambda x: x['label'])
        team_market_groups = sorted(team_market_groups, key=lambda x: x['label'])
        rounds = sorted(rounds, key=lambda x: x['value'])        

        filters = {
            'round': rounds,
            'theme': themes,
            'agency': agencies,
            'account': accounts2,
            'key_account': key_account,
            'market': markets,
            'market_country': countries,
            'market_region': regions,
            'survey': surveys,
            'contact_type': contact_types,
            'contact_division': contact_divisions,
            'team': teams,
            'team_type': team_types,
            'team_market': teaf_markets,
            'team_reach': team_market_groups,
            'sector': sectors,
        }
        
        return filters, contacts, f_accounts, f_market_countries, f_sectors


    async def get_norms(self, markets: dict, sectors: dict, barometer: bool) -> dict:
        norm = {}
        norm_key = []
        # build up list of unique markets and sectors
        norm_markets = set([market for market in markets.keys()])
        norm_sectors = set([sector for sector in sectors.keys()])

        # if we have market filters applied, create unique list of them
        # if not, use the list created above
        if self.global_filters.get('market_country_id'):
            norm_markets = set([market for market in self.global_filters['market_country_id']])

        # if we have sector filters applied, create unique list of them
        # if not, use the list created above
        if self.global_filters.get('sector'):
            norm_sectors = set([sector.replace(' ', '_').lower() for sector in self.global_filters['sector']])

        # if list is 1, add it to the norm_key
        if len(norm_markets) == 1:
            market_name = markets.get(list(norm_markets)[0])
            if market_name:
                # FIXME: this is messy, fix it
                market_name = market_name.replace(' ', '').replace('&', '').replace(' and ', '').lower()
                norm_key.append(market_name)

        # if list is > 1, check to see if all markets belong in same region and add that to the norm_key
        if len(norm_markets) > 1:
            region = await self.db.markets.find_one({'type': 'Key Region', 'children': {'$all': list(norm_markets)}}, {'name': 1})
            if region:
                # FIXME: this is messy, fix it
                region_key = region['name'].replace(' and ', '').replace('&', '').replace(' ', '').lower()
                norm_key.append(region_key)

        if len(norm_sectors) == 1:
            sector_name = sectors.get(list(norm_sectors)[0])
            sector_name = sector_name.replace(' ', '').replace('&', '').replace(' and ', '').lower()
            norm_key.append(sector_name)

        # we couldn't get a norm key, so we'll just default to "overall"
        if not norm_key:
            norm_key.append('overall')

        norm_key = '-'.join(norm_key)
        
        # get the norm from the db
        # FIXME: this conditional is a temporary check for Omnicom demo and validation
        if self.user.internal or 'norms' in self.user.features:
            query = {'norm_key': norm_key}
            query['survey_type'] = 'trr' if not barometer else 'barometer'
            norm = await self.db.norms.find_one(query)
            if not norm and barometer:
                # we may only have the overall norm for barometer
                # try to fetch the overall norm if we didn't find a granular match
                query['norm_key'] = 'overall'
                norm = await self.db.norms.find_one(query)

        # Last safety check incase no norm was found
        if not norm:
            norm = {}

        return norm


    async def handler(self):
        start_time_all = time.time()
        accounts: list = self.user.account_hierarchy
        reporting_views: dict = self.user.reporting_views
        theme_dashboard: bool = False
        dashboard_result = {
            'status': '',
            'metadata': {
                'client': '',
                'norm': {},
                'theme_dashboard': theme_dashboard,
                'vertical': self.user.account_vertical
            },
            'filters': {'round': []},
            'dashboard': {},
            'contacts': [],
        }

        # FIXME: move this to a route guard
        if 'dashboards' not in self.user.features:
            raise PermissionDeniedException('User does not have correct features to access dashboards')

        # FIXME: move this to a route guard
        if not (self.user.roles.get('insights_viewer') or self.user.roles.get('panel_manager')):
            raise PermissionDeniedException('User does not have correct roles to access dashboards')

        # FIXME: move this to a route guard
        if not reporting_views:
            raise PermissionDeniedException('No reporting role found for user')

        # permission check to see if any of the account_id are in the accounts list
        if self.global_filters.get('account_id', []) and not set(self.global_filters['account_id']).issubset(set(accounts)):
            raise PermissionDeniedException('Account not in user account hierarchy')
        
        # Support team names (where applicable)
        client_title  = self.user.account_name
        if self.user.team_name:
            client_title = f'{client_title} ({self.user.team_name})'
        dashboard_result['metadata']['client'] = client_title

        #FIXME: move to contact agent
        # generate user round index key
        round_index_key = f'{self.user.account_organisation_level}_round_index'
        round_index_key = round_index_key.lower()
        round_index_key = round_index_key.replace(' ', '_').replace('-', '_')
        round_index_key = re.sub(r'\s*\(.*?\)', '', round_index_key)
        
        # get the dashboard from the db
        dashboard_config = await self.db.dashboards.find_one({'type': self.dashboard_type}, self.dashboard_config_projection)
        if not dashboard_config:
            dashboard_result['status'] = 'nodata'
            return dashboard_result

        dashboard_result['dashboard'] = dashboard_config
        # get the type of response data this dashboard requires
        data_required = get_response_data_required(dashboard_config)
        barometer_dashboard = True if 'barometer' in data_required else False
        # if the dashboard shows barometer data, check the user has permissions to view it
        if 'barometer' in data_required and 'dashboards_barometer' not in self.user.features:
            raise PermissionDeniedException('User does not have correct feature to access barometer data')

        # build the base query for the dashboard cards (hard filters)
        reporting_views_trr = get_reporting_views(reporting_views, False)
        reporting_views_barometer = get_reporting_views(reporting_views, True)
        response_query_trr = build_response_query(reporting_views_trr, self.global_filters, self.user, round_index_key)
        response_query_barometer = build_response_query(reporting_views_barometer, self.global_filters, self.user)
        response_projection = get_response_projection(self.base_response_projection_fields, dashboard_config.get('cards', []), round_index_key)

        # get response data
        start_time = time.time()

        trr_response_data = []
        barometer_response_data = []
        if 'trr' in data_required:
            trr_response_data = await fetch_responses(self.db.responses, response_query_trr, response_projection)
        if 'barometer' in data_required:
            barometer_response_data = await fetch_responses(self.db.responsesbarometer, response_query_barometer, response_projection)
        elapsed_time = time.time() - start_time
        print(f"DASHBOARD ({self.dashboard_type}) FETCH DATA: {elapsed_time:.4f} seconds, TRR RECORDS: {len(trr_response_data)}, Barometer RECORDS: {len(barometer_response_data)}")
        if len(trr_response_data) == 0 and len(barometer_response_data) == 0:
            dashboard_result['status'] = 'nodata'
            return dashboard_result

        # generate round lookup
        # FIXME: moved this out fitlers iteration as we need to default the round index
        #       but don't want two iterations over this large dataset, so this needs sorted
        rounds_lookup_trr = get_round_lookup(trr_response_data, round_index_key)
        rounds_lookup_barometer = get_round_lookup(barometer_response_data, round_index_key)
        # if no round filter was applied, we can't assume default to 0, so default to latest available round
        if not self.global_filters.get('__round_index'):
            if barometer_dashboard:
                self.global_filters['__round_index'] = min(rounds_lookup_barometer.keys()) if rounds_lookup_barometer else 0
            else:
                self.global_filters['__round_index'] = min(rounds_lookup_trr.keys()) if rounds_lookup_trr else 0

        # generate filters and build set of contacts (for search)
        # when we have barometer
        responses_for_filters = barometer_response_data if barometer_dashboard else trr_response_data
        rounds_lookup_for_filters = rounds_lookup_barometer if barometer_dashboard else rounds_lookup_trr
        # no contact search in barometer dashboards
        build_contacts = False if barometer_dashboard else True
        filters, contacts, accounts_lookup, markets_lookup, sectors_lookup = self.generate_filters_and_contacts(round_index_key, rounds_lookup_for_filters, self.global_filters.get('__round_index'), responses_for_filters, build_contacts)

        # panel manager access check -> if insights date is in the future, bail
        if self.user.roles.get('panel_manager') and not self.user.roles.get('insights_viewer'):
            active_round = None
            if barometer_dashboard:
                active_round = rounds_lookup_barometer.get(self.global_filters.get('__round_index'))
            else:
                active_round = rounds_lookup_trr.get(self.global_filters.get('__round_index'))
        
            if not active_round or not active_round.get('insights_date') or date.today() < active_round.get('insights_date').date():
                dashboard_result['status'] = 'lockout'
                return dashboard_result

        trr_norm = await self.get_norms(markets_lookup, sectors_lookup, False)
        barometer_norm = await self.get_norms(markets_lookup, sectors_lookup, True)

        async def fetch_card_data(idx, card, trr_responses, barometer_responses, self):
            start_time = time.time()
            card_id = str(idx)

            # check it has a type -> skip if not
            if 'type' not in card:
                return None

            # get local filters for specific card
            local_filters = self.local_filters.get(card_id, {})

            try:
                card_data_source = import_module(f'{self.card_type_modules_path}.{card["type"]}')
                card_class = getattr(card_data_source, card['type'])
                card_instance = card_class(trr_data=trr_responses,
                                           barometer_data=barometer_responses,
                                           card_type=card.get('type'),
                                           card_query=card.get('query', {}),
                                           card_projection=card.get('projection', {}),
                                           card_limit=card.get('limit'),
                                           card_filters=card.get('filters', []),
                                           card_non_response=card.get('include_non_response', False),
                                           card_previous_rounds=card.get('previous_rounds'),
                                           card_data_required=card.get('data_required', ['trr']),
                                           filtered_round=self.global_filters.get('__round_index'),
                                           global_filters=self.global_filters, 
                                           local_filters=local_filters,
                                           round_index_key=round_index_key,
                                           trr_norm=trr_norm,
                                           barometer_norm=barometer_norm,)
                card_data = card_instance.fetch()
                card['i'] = card_id
                card['data'] = card_data
                card['vertical'] = self.user.account_vertical

                end_time = time.time()
                elapsed_time = end_time - start_time
                print(f'>>> DASHBOARD CARD PROCESSED: {card.get('type')} - {elapsed_time}')

                return card
            except (ImportError, AttributeError) as e:
                print(f'Error importing card data source: {e}')
                return None

        async def run_parallel(self, dashboard_config, trr_responses, barometer_responses):
            cards = dashboard_config.get('cards', [])
            tasks = []

            for idx, card in enumerate(cards):
                tasks.append(fetch_card_data(idx, card, trr_responses, barometer_responses, self))

            results = await asyncio.gather(*tasks, return_exceptions=True)

            dashboard_config['cards'] = [result for result in results if result is not None and not isinstance(result, Exception)]

            return dashboard_config

        # FIXME: this is a hack and should be removed when the dashboard is refactored
        if self.dashboard_type == 'themeanalysis':
            theme_dashboard = True
            if not self.global_filters.get('theme_categories') and len(filters['theme']) > 0:
                self.global_filters['theme_categories'] = [filters['theme'][0]['value']]
        
        # FIXME: move to response agent
        if self.global_filters.get('team_market_group') and 'Local' in self.global_filters['team_market_group']:
            self.global_filters.get('team_market_group').append(None)

        # Get dashboard cards
        print(f'>>> DASHBOARD USER: {self.user.email}')
        print(f'>>> DASHBOARD CARDS REQUESTED: {len(dashboard_config.get('cards', []))}')
        start_time_dashboard = time.time()
        dashboard = await run_parallel(self, dashboard_config, trr_response_data, barometer_response_data)
        end_time_dashboard = time.time()
        elapsed_time_dashboard = end_time_dashboard - start_time_dashboard
        print(f'>>> DASHBOARD CARD TOTAL: {elapsed_time_dashboard}')

        # FIXME: this is a hack and should be removed when the dashboard is refactored
        if self.dashboard_type == 'contact' and self.global_filters.get('contact_id') and len(self.global_filters.get('contact_id', [])) > 0:
            panel_member = await self.db.contacts.find_one({'Id': self.global_filters['contact_id'][0]}, {'_id': 0, 'FirstName': 1, 'LastName': 1, 'Title': 1, 'AccountId': 1})
            if panel_member:
                dashboard['title'] = f'{panel_member["FirstName"]} {panel_member["LastName"]}'
                dashboard['job_title'] = panel_member["Title"]
                dashboard['account_name'] = accounts_lookup.get(panel_member["AccountId"], "")
                dashboard['markets'] = filters['market']

        # FIXME: this is a hack and should be removed when the dashboard is refactored
        if self.dashboard_type == 'themeanalysis':
            theme_analysis_title = 'Theme'
            if self.global_filters.get('theme_categories', []):
                theme_analysis_title = f'Theme: {self.global_filters.get('theme_categories', [])[0]}'
            dashboard['title'] = theme_analysis_title

        end_time_all = time.time()
        elapsed_time_all = end_time_all - start_time_all
        print(f"DASHBOARD ({self.dashboard_type}) LOAD TIME TOTAL: {elapsed_time_all:.4f} seconds for user {self.user.id}")

        dashboard_result['status'] = 'success'
        dashboard_result['metadata']['norm'] = trr_norm.get('norm_key') if not barometer_dashboard else barometer_norm.get('norm_key')
        dashboard_result['metadata']['theme_dashboard'] = theme_dashboard
        dashboard_result['filters'] = filters
        dashboard_result['contacts'] = contacts
        dashboard_result['dashboard'] = dashboard
        return dashboard_result
