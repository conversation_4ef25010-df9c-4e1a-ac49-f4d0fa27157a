from api.clientarea.src.utils.cards.Card import Card


class RatingDistributionTrend360(Card):


    def build_data(self, data: dict) -> list:
        response_percentages = []
        brackets = data['brackets']
        total_ratings = data['total_ratings']
        total_percentage = 0

        for idx, key in enumerate(brackets):
            value = brackets[key]
            percentage = round((value / total_ratings) * 100, 0) if total_ratings > 0 else 0
            if idx == len(brackets) - 1 and percentage != 0:
                # if this is the last bracket and percentage is not 0, we need to adjust the last percentage
                # to ensure it sums to 100
                percentage = 100 - total_percentage
            total_percentage += percentage
            response_percentages.append(percentage)
        
        return response_percentages


    def get_data(self, data:list, round_index:str, rounds:dict = {}, filters:dict = {}, norm:dict = {}):    
        result_trr = {
            'brackets': {
                'optout': 0,
                '1-4': 0,
                '5-6': 0,
                '7': 0,
                '8': 0,
                '9-10': 0,
            },
            'total_ratings': 0
        }
        result_barometer = {
            'brackets': {
                'optout': 0,
                '1-4': 0,
                '5-6': 0,
                '7': 0,
                '8': 0,
                '9-10': 0,
            },
            'total_ratings': 0
        }
        barometer_norms = {}
        if self.barometer_norm:
            barometer_norm_labels = [
                'rating_distribution_optout',
                'rating_distribution_1_4',
                'rating_distribution_5_6',
                'rating_distribution_7',
                'rating_distribution_8',
                'rating_distribution_9_10',
            ]
            norm_breakdown_barometer = [float(self.barometer_norm.get(label, 0)) for label in barometer_norm_labels]
            barometer_norms['response'] = norm_breakdown_barometer
        trr_norms = {}
        if norm:
            trr_norm_labels = [
                'opt_out_percentage',
                'active_detractor_percentage',
                'passive_detractor_percentage',
                'passive_supporter_percentage',
                'passive_advocate_percentage',
                'active_advocate_percentage',
            ]
            norm_breakdown_trr = [float(norm.get(label, 0)) for label in trr_norm_labels]
            trr_norms['response'] = norm_breakdown_trr
        
        for record in data:
            result = result_trr if not record.get('is_barometer') else result_barometer
            # handle optout case first
            if record['contact_opt_out']:
                result['brackets']['optout'] += 1
                result['total_ratings'] += 1
                continue
        
            result['total_ratings'] += 1
            rating = 0 if not record.get('rating', None) else record.get('rating', 0)
            
            if rating >= 0 and rating < 5:
                result['brackets']['1-4'] += 1
            elif rating >= 5 and rating < 7:
                result['brackets']['5-6'] += 1
            elif rating >= 7 and rating < 8:
                result['brackets']['7'] += 1
            elif rating >= 8 and rating < 9:
                result['brackets']['8'] += 1
            elif rating >= 9 and rating < 10:
                result['brackets']['9-10'] += 1

        response_breakdown_trr = self.build_data(result_trr)
        response_breakdown_barometer = self.build_data(result_barometer)

        return {
            'latest_trr': {'response': response_breakdown_trr},
            'latest_barometer': {'response': response_breakdown_barometer},
            'norms_trr': trr_norms,
            'norms_barometer': barometer_norms,
        }