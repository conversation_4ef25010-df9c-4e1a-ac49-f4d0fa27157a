import copy
from api.clientarea.src.utils.cards.Card import Card


class RatingByAccount360(Card):

    DEFAULT_DATA = {
        'name': '',
        'id': '',
        'responses_trr': 0,
        'responses_barometer': 0,
        'rating_trr': 0,
        'rating_barometer': 0,
        'rating_avg_trr': 0,
        'rating_avg_barometer': 0,
        # 'response_rate': 0,
        'responses_with_themes_trr': 0,
        'responses_with_themes_barometer': 0,
        'lr_responses_trr': 0,
        'lr_rating_trr': 0,
        'lr_rating_avg_trr': 0,
        'lr_responses_barometer': 0,
        'lr_rating_barometer': 0,
        'lr_rating_avg_barometer': 0,
        'lr_responses_with_themes_trr': 0,
        'lr_responses_with_themes_barometer': 0,
        'sentiment_p_trr': 0,
        'sentiment_n_trr': 0,
        'sentiment_m_trr': 0, 
        'sentiment_p_barometer': 0,
        'sentiment_n_barometer': 0,
        'sentiment_m_barometer': 0, 
        'lr_sentiment_p_trr': 0,
        'lr_sentiment_n_trr': 0,
        'lr_sentiment_m_trr': 0,
        'lr_sentiment_p_barometer': 0,
        'lr_sentiment_n_barometer': 0,
        'lr_sentiment_m_barometer': 0,
        'total_feedback': 0,
        'feedback_relational': 0,
        'feedback_transactional': 0,
        'feedback_general': 0,
        'lr_total_feedback': 0,
        'lr_feedback_relational': 0,
        'lr_feedback_transactional': 0,
        'lr_feedback_general': 0,
        'total_surveyed_trr': 0,
        'total_surveyed_barometer': 0,
        'in_latest_round': False,
    }


    def __round_to_100(self, values):
        rounded_values = [round(value, 2) for value in values]
        total = sum(rounded_values)
    
        difference = round(100 - total, 2)
    
        if difference != 0:
            max_index = max(range(len(rounded_values)), key=lambda i: rounded_values[i])
            rounded_values[max_index] += difference
            rounded_values[max_index] = round(rounded_values[max_index], 2)
        
        return rounded_values
    

    def __rating_change(self, this_round, last_round):
        if this_round == 0 or last_round == 0:
            return 0
        increase = round(this_round, 2) - round(last_round, 2)
        percentage_increase = (increase / last_round) * 100
        return round(percentage_increase, 2)
    
    
    def __calculate_response_rate(self, responses, total):
        if total == 0:
            return 0
        response_rate = round((responses / total) * 100, 2)
        response_rate = int(response_rate) if response_rate.is_integer() else response_rate
        return response_rate


    def __calculate_point_difference(self, this_round, last_round):
        if this_round == 0:
            return 0
        return round(this_round - last_round, 2)


    def __calculate_percentage_difference(self, val_a, val_b):
        if val_a == 0 and val_b == 0:
            return 0
        return round((val_a - val_b) / ((val_a + val_b) / 2) * 100, 1)


    def calc_aggregate_metrics(self, responses: list, dimension_name: str, round_index: str, rounds_lookup: dict) -> list:
        grouped_data = {}
        id_property = f'{dimension_name}_id'
        name_property = f'{dimension_name}_name'
        trr_csr_ids_by_round = {}

        if dimension_name == 'market_country':
            # country name is stored as market_country, not market_country_name
            # TODO should probably fix this in the sync response agent, but will mean updating dashboard config in db to match
            name_property = 'market_country'

        # Note: data is ordered so that barometer responses are always before trr responses
        for record in responses:
            suffix = '_trr' if not record.get('is_barometer') else '_barometer'
            index = record[round_index]
            if record.get('is_barometer'):
                trr_csr_ids_by_round[index] = record['linked_trr_survey_id']
            else:
                # it's safe to match trr data here as the barometer data has already been processed
                match_found = False
                for barometer_round_id, value in trr_csr_ids_by_round.items():
                    if record['customer_survey_round_id'] == value:
                        index = barometer_round_id
                        match_found = True
                        break
                if not match_found:
                    continue

            dimension_id = record.get(id_property)
            name = record.get(name_property)
            if not dimension_id:
                dimension_id = 'unknown'
                name = 'Unknown'

            if name not in grouped_data:
                grouped_data[name] = copy.copy(self.DEFAULT_DATA)
                grouped_data[name]['name'] = name
                grouped_data[name]['id'] = dimension_id

            aggregate_data = grouped_data[name]

            # if it's the latest round, count-up the total surveyed
            if rounds_lookup[index] == 'current':
                aggregate_data['total_surveyed'+suffix] += 1

            if rounds_lookup[index] == 'current':
                aggregate_data['responses'+suffix] += 1
                aggregate_data['rating'+suffix] += 0 if not record.get('rating', None) else record.get('rating', 0)
                aggregate_data['rating_avg'+suffix] = aggregate_data['rating'+suffix] / aggregate_data['responses'+suffix]
                aggregate_data['in_latest_round'] = True

                if record.get('sentiment'):
                    aggregate_data['responses_with_themes'+suffix] += 1

                    if record['sentiment'] == 'p':
                        aggregate_data['sentiment_p'+suffix] += 1
                    elif record['sentiment'] == 'n':
                        aggregate_data['sentiment_n'+suffix] += 1
                    elif record['sentiment'] == 'm':
                        aggregate_data['sentiment_m'+suffix] += 1
            elif rounds_lookup[index] == 'previous':
                aggregate_data['lr_responses'+suffix] += 1
                aggregate_data['lr_rating'+suffix] += 0 if not record.get('rating', None) else record.get('rating', 0)
                aggregate_data['lr_rating_avg'+suffix] = aggregate_data['lr_rating'+suffix] / aggregate_data['lr_responses'+suffix]

                if record.get('sentiment'):
                    aggregate_data['lr_responses_with_themes'+suffix] += 1

                    if record['sentiment'] == 'p':
                        aggregate_data['lr_sentiment_p'+suffix] += 1
                    elif record['sentiment'] == 'n':
                        aggregate_data['lr_sentiment_n'+suffix] += 1
                    elif record['sentiment'] == 'm':
                        aggregate_data['lr_sentiment_m'+suffix] += 1
            
        # strip out any grouping not in the latest round
        grouped_data = {k: v for k, v in grouped_data.items() if v['in_latest_round']}
        
        for suffix in ['_trr', '_barometer']:
            for _, aggregate_data in grouped_data.items():
                if aggregate_data['responses_with_themes'+suffix] > 0:
                    sentiment_rounded_values = self.__round_to_100([
                        (aggregate_data['sentiment_p'+suffix] / aggregate_data['responses_with_themes'+suffix] * 100),
                        (aggregate_data['sentiment_n'+suffix] / aggregate_data['responses_with_themes'+suffix] * 100),
                        (aggregate_data['sentiment_m'+suffix] / aggregate_data['responses_with_themes'+suffix] * 100),
                    ])
                    aggregate_data['sentiment_p'+suffix] = sentiment_rounded_values[0]
                    aggregate_data['sentiment_n'+suffix] = sentiment_rounded_values[1]
                    aggregate_data['sentiment_m'+suffix] = sentiment_rounded_values[2]

                if aggregate_data['lr_sentiment_p'+suffix] > 0:
                    aggregate_data['lr_sentiment_p'+suffix] = round((aggregate_data['lr_sentiment_p'+suffix] / aggregate_data['lr_responses_with_themes'+suffix] * 100), 2)
                if aggregate_data['lr_sentiment_n'+suffix] > 0:
                    aggregate_data['lr_sentiment_n'+suffix] = round((aggregate_data['lr_sentiment_n'+suffix] / aggregate_data['lr_responses_with_themes'+suffix] * 100), 2)
                if aggregate_data['lr_sentiment_m'+suffix] > 0:
                    aggregate_data['lr_sentiment_m'+suffix] = round((aggregate_data['lr_sentiment_m'+suffix] / aggregate_data['lr_responses_with_themes'+suffix] * 100), 2)

        # sort the data by rating_avg
        sorted_data = sorted(grouped_data.values(), key=lambda x: x['rating_avg_trr'], reverse=True)
        return sorted_data


    def get_data(self, data:list, round_index:str, rounds:dict = {}, filters:dict = {}, norm:dict = {}):
        groupings = {
            'account': [], 
            'market_country': [],
        }
        if not data:
            return groupings
        
        for key in groupings.keys():
            groupings[key] = self.calc_aggregate_metrics(data, key, round_index, self.rounds_barometer)

        return groupings
    

    def format(self, data: dict):
        formatted_groupings = {}
        for dimension, data in data.items():
            formatted_data = [{
                'id': x['id'],
                'name': x['name'],
                'responses_trr': x['responses_trr'],
                'responses_barometer': x['responses_barometer'],
                'rating_avg_trr': round(x['rating_avg_trr'], 2),
                'rating_avg_barometer': round(x['rating_avg_barometer'], 2),
                'rating_delta': round(x['rating_avg_trr'] - x['rating_avg_barometer'], 2),
                'rating_delta_percent': self.__calculate_percentage_difference(x['rating_avg_trr'], x['rating_avg_barometer']),
                'response_rate_trr': f'{self.__calculate_response_rate(x['responses_trr'], x['total_surveyed_trr'])}%',
                'response_rate_barometer': f'{self.__calculate_response_rate(x['responses_barometer'], x['total_surveyed_barometer'])}%',
                'total_surveyed_trr': x['total_surveyed_trr'],
                'total_surveyed_barometer': x['total_surveyed_barometer'],
                'sentiment_positive_trr': x['sentiment_p_trr'],
                'sentiment_negative_trr': x['sentiment_n_trr'],
                'sentiment_mentioned_trr': x['sentiment_m_trr'],
                'sentiment_positive_barometer': x['sentiment_p_barometer'],
                'sentiment_negative_barometer': x['sentiment_n_barometer'],
                'sentiment_mentioned_barometer': x['sentiment_m_barometer'],
                'movement_sentiment_positive_trr': self.__calculate_point_difference(x['sentiment_p_trr'], x['lr_sentiment_p_trr']),
                'movement_sentiment_negative_trr': self.__calculate_point_difference(x['sentiment_n_trr'], x['lr_sentiment_n_trr']),
                'movement_sentiment_mixed_trr': self.__calculate_point_difference(x['sentiment_m_trr'], x['lr_sentiment_m_trr']),
                'movement_sentiment_positive_barometer': self.__calculate_point_difference(x['sentiment_p_barometer'], x['lr_sentiment_p_barometer']),
                'movement_sentiment_negative_barometer': self.__calculate_point_difference(x['sentiment_n_barometer'], x['lr_sentiment_n_barometer']),
                'movement_sentiment_mixed_barometer': self.__calculate_point_difference(x['sentiment_m_barometer'], x['lr_sentiment_m_barometer']),
            } for x in data]
            # ignore any groupings that only have an entry for 'unknown'
            if len(formatted_data) == 1 and formatted_data[0]['id'] == 'unknown':
                continue
            # ignore any entries in the groupings that have no trr or barometer responses
            formatted_data = [x for x in formatted_data if x['responses_trr'] > 0 and x['responses_barometer'] > 0]
            formatted_groupings[dimension] = formatted_data

        return formatted_groupings
