from api.clientarea.src.utils.cards.Card import Card


class Rating360(Card):

    def get_data(self, data:list, round_index:str, rounds:dict = {}, filters:dict = {}, norm:dict = {}):
        responses_trr = 0
        responses_baro = 0
        total_rating_trr = 0
        total_rating_baro = 0
        average_rating_trr = 0
        average_rating_baro = 0
        rating_percent_diff = 0
        trr_csr_id = None

        # Note: data is ordered so that barometer responses are always before trr responses,
        # so we can grab the trr csr id from the barometer responses
        for record in data:
            rating = 0 if not record.get('rating', None) else record.get('rating', 0)

            if record.get('is_barometer'):
                trr_csr_id = record['linked_trr_survey_id']
                if record['responded']:
                    responses_baro += 1
                    total_rating_baro += rating
            else:
                # process a TRR response if it links to the latest barometer linked CSR id
                if trr_csr_id and record['customer_survey_round_id'] in self.trr_csr_ids:
                    if record['responded']:
                        responses_trr += 1
                        total_rating_trr += rating
        
        if responses_trr > 0:
            average_rating_trr = round(total_rating_trr / responses_trr, 2)
        if responses_baro > 0:
            average_rating_baro = round(total_rating_baro / responses_baro, 2)

        rating_delta = round(average_rating_baro - average_rating_trr, 2)
        if average_rating_baro > 0 and average_rating_trr > 0:
            rating_percent_diff = (average_rating_baro - average_rating_trr) / ((average_rating_trr + average_rating_baro)/2) * 100
            rating_percent_diff = round(rating_percent_diff, 1)

        return {
            'rating_delta': rating_delta,
            'rating_percent_diff': rating_percent_diff,
            'rating_trr': average_rating_trr,
            'rating_barometer': average_rating_baro,
            'norm_rating_trr': float(self.trr_norm.get('ccr', 0)),
            'norm_rating_barometer': float(self.barometer_norm.get('ccr', 0)),
        }