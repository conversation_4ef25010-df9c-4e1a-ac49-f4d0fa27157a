from api.clientarea.src.utils.cards.Card import Card


class OverallRatingTRR360(Card):
    """ Overall rating widget for TRR responses linked to barometer survey rounds
    """
    def __calculate_avg_rating(self, rating, responses):
        if responses == 0:
            return 0
        return round(rating / responses, 2)


    def __calculate_response_rate(self, responses, total):
        if total == 0:
            return 0
        return round((responses / total) * 100, 2)
    

    def __calculate_metric_movement(self, this_round, last_round):
        if this_round == 0 or last_round == 0:
            return 0
        increase = this_round - last_round
        percentage_increase = (increase / last_round) * 100
        return round(percentage_increase, 2)
    

    def __calculate_point_difference(self, this_round, last_round):
        if this_round == 0:
            return 0
        return round(this_round - last_round, 2)
    

    def get_data(self, data:list, round_index:str, rounds:dict = {}, filters:dict = {}, norm:dict = {}):
        latest_round_total_surveyed = 0
        latest_round_responses = 0
        latest_round_total_rating = 0
        last_round_total_surveyed = 0
        last_round_responses = 0
        last_round_total_rating = 0
        trr_csr_ids_by_round = {}

        result = {
            'rating': 0,
            'response_rate': 0,
            'responses': 0,
            'total_surveyed': 0,
            'rating_movement': 0,
            'response_movement': 0,
            'last_round_response_rate': 0,
            'last_round_responses': 0,
            'last_round_total_surveyed': 0,
            'last_round_rating': 0,
            'rating_norm': 0,
            'response_norm': 0,
            # FIXME: this is a temporary check for Omnicom demo and validation
            'norm_enabled': bool(norm),
            'data_hidden': False
        }

        # Note: data is ordered so that barometer responses are always before trr responses
        for record in data:
            index = record[round_index]
            rating = 0 if not record.get('rating', None) else record.get('rating', 0)

            if record.get('is_barometer') and trr_csr_ids_by_round.get(index) is not None:
                # we've added the barometer match data for this round so skip
                continue
            if record.get('is_barometer'):
                # this is a barometer response, so we need to add the trr csr id
                trr_csr_ids_by_round[index] = record['linked_trr_survey_id']
                continue

            # now we're processing trr data, we need to check if the CSR id matches the barometer linked CSR id
            match_found = False
            for barometer_round_id, value in trr_csr_ids_by_round.items():
                if record['customer_survey_round_id'] == value:
                    index = barometer_round_id
                    match_found = True
                    break
            if not match_found:
                continue

            # latest round metrics
            if self.rounds_barometer[index] == 'current':
                latest_round_total_surveyed += 1

                if record['responded']:
                    latest_round_responses += 1
                    latest_round_total_rating += rating
            
            # last round metrics
            if self.rounds_barometer[index] == 'previous':
                last_round_total_surveyed += 1

                if record['responded']:
                    last_round_responses += 1
                    last_round_total_rating += rating
        
        result['rating'] = self.__calculate_avg_rating(latest_round_total_rating, latest_round_responses)
        result['response_rate'] = self.__calculate_response_rate(latest_round_responses, latest_round_total_surveyed)
        result['responses'] = latest_round_responses
        result['total_surveyed'] = latest_round_total_surveyed

        if last_round_responses > 0:
            result['last_round_rating'] = self.__calculate_avg_rating(last_round_total_rating, last_round_responses)
            result['last_round_response_rate'] = self.__calculate_response_rate(last_round_responses, last_round_total_surveyed)
            result['last_round_responses'] = last_round_responses
            result['last_round_total_surveyed'] = last_round_total_surveyed
            result['rating_movement'] = self.__calculate_metric_movement(result['rating'], result['last_round_rating'])
            result['response_movement'] = self.__calculate_point_difference(result['response_rate'], result['last_round_response_rate'])

        if norm:
            result['rating_norm'] = self.__calculate_metric_movement(result['rating'], float(norm.get('ccr', 0)))
            result['response_norm'] = self.__calculate_point_difference(result['response_rate'], float(norm.get('response_rate', 0)))

        return result
