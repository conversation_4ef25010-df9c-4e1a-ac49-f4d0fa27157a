from api.clientarea.src.utils.cards.Card import Card


class SentimentDistribution360(Card):

    def __round_to_100(self, values):
        rounded_values = [round(value, 2) for value in values]
        total = sum(rounded_values)
    
        difference = round(100 - total, 2)
    
        if difference != 0:
            max_index = max(range(len(rounded_values)), key=lambda i: rounded_values[i])
            rounded_values[max_index] += difference
            rounded_values[max_index] = round(rounded_values[max_index], 2)
        
        return rounded_values
    

    def update_sentiment(self, record, total_sentiment):
        total_sentiment['responses'] += 1

        p_count = 0
        n_count = 0
        for theme in record.get('themes'):
            if not theme.get('pn'):
                # null or empty string
                continue
            if int(theme['pn']) > 0:
                p_count += 1
            elif int(theme['pn']) < 0:
                n_count += 1

        if p_count > 0 and n_count == 0:
            total_sentiment['response_with_themes'] += 1
            total_sentiment['positive_only'] += 1
            total_sentiment['positive_count'] += 1
        elif p_count == 0 and n_count > 0:
            total_sentiment['response_with_themes'] += 1
            total_sentiment['negative_only'] += 1
            total_sentiment['negative_count'] += 1
        elif p_count > 0 and n_count > 0:
            total_sentiment['response_with_themes'] += 1
            total_sentiment['mixed'] += 1
            total_sentiment['mixed_count'] += 1


    def update_categories(self, record, categories):
        for theme in record.get('themes', []):
            category = theme['l1']
            if not category:
                continue


            if category == 'Relational':
                categories['total_feedback_trr'] += 1
                categories['feedback_relational'] += 1
            elif category == 'Transactional':
                categories['total_feedback_trr'] += 1
                categories['feedback_transactional'] += 1
            elif category == 'Agency Focus':
                categories['total_feedback_barometer'] += 1
                categories['feedback_internal_focused'] += 1
            elif category == 'Client Focus':
                categories['total_feedback_barometer'] += 1
                categories['feedback_client_focused'] += 1
            else:
                categories['total_feedback_trr'] += 1
                categories['feedback_general'] += 1


    def get_data(self, data:list, round_index:str, rounds:dict = {}, filters:dict = {}, norm:dict = {}):
        total_sentiment = {
            'responses': 0,
            'response_with_themes': 0,
            'positive_only': 0,
            'positive_count': 0,
            'positive_percentage': 0,
            'negative_only': 0,
            'negative_count': 0,
            'negative_percentage': 0,
            'mixed': 0,
            'mixed_count': 0,
            'mixed_percentage': 0,
        }
        categories = {
            'total_feedback_trr': 0,
            'total_feedback_barometer': 0,
            'feedback_relational': 0,
            'feedback_relational_percentage': 0,
            'feedback_transactional': 0,
            'feedback_transactional_percentage': 0,
            'feedback_general': 0,
            'feedback_general_percentage': 0,
            # barometer groupings
            'feedback_internal_focused': 0,
            'feedback_internal_focused_percentage': 0,
            'feedback_client_focused': 0,
            'feedback_client_focused_percentage': 0,
        }
        trr_csr_id = None

        # Note: data is ordered so that barometer responses are always before trr responses
        for record in data:
            if record.get('is_barometer'):
                # this is a barometer response, so we need to add the trr csr id
                trr_csr_id = record['linked_trr_survey_id']

            if 'trr' in self.card_data_required:
                # widget is showing trr data, discard barometer responses at this point
                if record.get('is_barometer'):
                    continue
                # process a TRR response if it links to the latest barometer linked CSR id
                if trr_csr_id and record['customer_survey_round_id'] in self.trr_csr_ids:
                    match_found = True
                if not match_found:
                    continue

            self.update_sentiment(record, total_sentiment)
            self.update_categories(record, categories)

        if total_sentiment['response_with_themes']:
            rounded_values = self.__round_to_100([
                (total_sentiment['positive_only'] / total_sentiment['response_with_themes'] * 100),
                (total_sentiment['mixed'] / total_sentiment['response_with_themes'] * 100),
                (total_sentiment['negative_only'] / total_sentiment['response_with_themes'] * 100),
            ])
            total_sentiment['positive_percentage'] = rounded_values[0]
            total_sentiment['negative_percentage'] = rounded_values[1]
            total_sentiment['mixed_percentage'] = rounded_values[2]

        if categories['total_feedback_trr']:
            rounded_values = self.__round_to_100([
                (categories['feedback_relational'] / categories['total_feedback_trr'] * 100),
                (categories['feedback_transactional'] / categories['total_feedback_trr'] * 100),
                (categories['feedback_general'] / categories['total_feedback_trr'] * 100),
            ])
            categories['feedback_relational_percentage'] = rounded_values[0]
            categories['feedback_transactional_percentage'] = rounded_values[1]
            categories['feedback_general_percentage'] = rounded_values[2]
        if categories['total_feedback_barometer']:
            rounded_values = self.__round_to_100([
                (categories['feedback_internal_focused'] / categories['total_feedback_barometer'] * 100),
                (categories['feedback_client_focused'] / categories['total_feedback_barometer'] * 100),
            ])
            categories['feedback_internal_focused_percentage'] = rounded_values[0]
            categories['feedback_client_focused_percentage'] = rounded_values[1]

        return {'total_sentiment': total_sentiment, 'categories': categories}


    def format(self, data: dict):
        sentiment = data['total_sentiment']

        result = {
            'barometer': True if 'trr' not in self.card_data_required else False,
            'sentiment': {
                'positive_count': sentiment['positive_count'],
                'positive_percentage': sentiment['positive_percentage'],
                'negative_count': sentiment['negative_count'],
                'negative_percentage': sentiment['negative_percentage'],
                'mixed_count': sentiment['mixed_count'],
                'mixed_percentage': sentiment['mixed_percentage']
            },
            'categories': data['categories'],
        }
        return result
