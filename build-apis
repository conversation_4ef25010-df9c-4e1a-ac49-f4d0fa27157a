#!/usr/bin/env python
import subprocess
import argparse

ap = argparse.ArgumentParser()
ap.add_argument('stack')
args = ap.parse_args()

aws_region = 'eu-west-1'
stack = args.stack
ecr_repo_base = '017820661481.dkr.ecr.eu-west-1.amazonaws.com'
ecr_repo_name = f'crc-backend-apis-eu-west-1-{stack}'
ecr_repo_url = f"{ecr_repo_base}/{ecr_repo_name}"

subprocess.check_call(['poetry', 'export', '--without-hashes', '-f', 'requirements.txt', '-o', './requirements.txt'])
subprocess.check_call(['docker', 'buildx', 'build', '-f', 'api/Dockerfile', '--platform', 'linux/arm64', '-t', f'{ecr_repo_name}:{stack}', '--provenance=false', '.'])
password = subprocess.check_output(['aws', 'ecr', 'get-login-password', '--region', aws_region])
subprocess.check_output(['docker', 'login', '--username', 'AWS', '--password-stdin', ecr_repo_base], input=password)
subprocess.check_call(['docker', 'tag', f'{ecr_repo_name}:{stack}', f'{ecr_repo_url}:{stack}'])
subprocess.check_call(['docker', 'push', f'{ecr_repo_url}:{stack}'])
