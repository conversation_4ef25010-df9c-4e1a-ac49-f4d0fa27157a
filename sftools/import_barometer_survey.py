""" Import the barometer survey Excel file populated by clients to set up a Barometer Survey
    This will be deprecated once the UI has been updated to allow Barometer Survey setup

DATA:
    FROM: File (xlsx or csv)
    TO: SF (Survey Round, Customer Survey Round, Customer Survey, Survey Client, Contact, Survey Panel Member)
"""
import csv
import os
import lib.sfapi as sfapi
import datetime
import argparse
from lib import sfimport
from lib.settings import settings
import openpyxl


def load_xlsx_panel_rows(filename) -> list:
    wb = openpyxl.open(filename, read_only=True)
    panel_sheet = None
    for ws in wb.worksheets:
        if ws.title.lower() == 'accounts and panel':
            panel_sheet = ws
            break
    if not panel_sheet:
        raise ValueError('Could not find the Accounts and Panel sheet')


    all_rows = list(ws.iter_rows(values_only=True))
    header_row = {}
    for col_idx, v in enumerate(all_rows[0]):
        if not v:
            continue
        v = v.lower().strip()
        v = v.split('\n')[0]
        header_row[v] = col_idx

    result = []
    result.append(header_row)
    for index, raw_row in enumerate(all_rows[1:]):
        row = {k: raw_row[v] if v < len(raw_row) else None for k, v in header_row.items()}
        row['row_number'] = index + 2
        row['reason'] = ''
        result.append(row)
    result[0]['row_number'] = col_idx + 1
    result[0]['reason'] = col_idx + 2
    return result


def load_csv_panel_rows(filename) -> list:
    result = []
    with open(filename, newline='', encoding='utf-8') as csvfile:
        reader = csv.DictReader(csvfile)
        field_names = reader.fieldnames + ['row_number', 'reason']
        header_row = {k.lower(): True for k in field_names}
        result.append(header_row)
        for index, row in enumerate(reader):
            clone = {k.lower(): v for k, v in row.items()}
            clone['row_number'] = index + 2
            clone['reason'] = ''
            result.append(clone)
    return result


duff_row_count = 0
def write_duff_row(writer, row, reason):
    global duff_row_count
    duff_row_count += 1
    row['reason']= reason
    writer.writerow(row)


def debug_output(detail: bool = False):
    # output summary info of what was done
    print(f'Importing {len(sfimport.new_survey_rounds)} new survey rounds')
    if detail:
        for survey_round in sfimport.new_survey_rounds:
            print(f'SURVEY ROUND: {survey_round.Name}') 
    print(f'Importing {len(sfimport.new_customer_survey_rounds)} new customer survey rounds')
    if detail:
        for customer_survey_round in sfimport.new_customer_survey_rounds:
            print(f'CUSTOMER SURVEY ROUND: {customer_survey_round.Name}')
    print(f'Importing {len(sfimport.new_customer_surveys)} new customer surveys') 
    if detail:
        for customer_survey in sfimport.new_customer_surveys:
            print(f'CUSTOMER SURVEY: {customer_survey.Name}')
    print(f'Importing {len(sfimport.new_survey_clients)} new survey clients')
    if detail:
        for survey_client in sfimport.new_survey_clients:
            print(f'SURVEY CLIENT: {survey_client.Name}')
    print(f'Importing {len(sfimport.new_customer_client_relationships)} new CCRs')
    if detail:
        for customer_client_relationship in sfimport.new_customer_client_relationships:
            print(f'CCR: {customer_client_relationship.Name}')
    print(f'Importing {len(sfimport.new_account_contact_relations)} new account contact relations')
    if detail:
        for account_contact_relation in sfimport.new_account_contact_relations:
            print(f'ACCOUNT CONTACT RELATION: ACC ID:{account_contact_relation.CustomerAccountId} CONTACT ID:{account_contact_relation.ContactId}')
    print(f'Importing {len(sfimport.new_contacts)} new contacts')
    if detail:
        for contact in sfimport.new_contacts:
            print(f'CONTACT: {contact.FirstName} {contact.LastName} {contact.Email}')
    print(f'Importing {len(sfimport.new_survey_panel_members)} new panel members')
    if detail:
        for survey_panel_member in sfimport.new_survey_panel_members:
            survey_client_name = None
            for item in sfimport.new_survey_clients:
                if item.Id == survey_panel_member.SurveyClientId:
                    survey_client_name = item.Name
                    break
            print(f'PANEL MEMBER: {survey_panel_member.Name} SURVEY CLIENT: {survey_client_name}')


def import_data(filename, importtype, surveystartdate, surveyenddate, safeemails, writeit, detailedoutput):
    # validate survey dates
    survey_start_date = datetime.date.fromisoformat(surveystartdate)
    survey_end_date = datetime.date.fromisoformat(surveyenddate)
    if survey_start_date >= survey_end_date:
        raise ValueError('Survey start date must be before survey end date')
    if survey_start_date <= datetime.datetime.now().date():
        raise ValueError('Survey start date must be in the future')
    if survey_end_date < survey_start_date + datetime.timedelta(days=6):
        raise ValueError('Survey end date must be at least 6 days after survey start date')

    # figure out dates of bits of the survey
    quarter_start_date = survey_start_date.replace(day=1, month=((survey_start_date.month - 1) // 3) * 3 + 1)
    survey_round_date = quarter_start_date
    survey_round_end_date = quarter_start_date + datetime.timedelta(days=30) # we need this value, but dunno what to set it to!
    # get default dates for the customer survey (they will be changed by PDT later)
    account_updates_start_date = survey_start_date - datetime.timedelta(days=5)
    account_updates_end_date = survey_start_date - datetime.timedelta(days=4)
    panel_updates_start_date = survey_start_date - datetime.timedelta(days=3)
    panel_updates_end_date = survey_start_date - datetime.timedelta(days=3)
    live_survey_first_request_date = survey_start_date + datetime.timedelta(days=2)
    live_survey_second_request_date = survey_start_date + datetime.timedelta(days=3)
    live_survey_third_request_date = survey_start_date + datetime.timedelta(days=4)
    live_survey_fourth_request_date = survey_start_date + datetime.timedelta(days=5)

    if filename.endswith('.xlsx'):
        # load data from the Excel file
        all_rows = load_xlsx_panel_rows(filename)
    elif filename.endswith('.csv'):
        # load data from csv file
        all_rows = load_csv_panel_rows(filename)
    else:
        raise Exception(f'Invalid file type {filename}')

    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)

    accountRecordTypes = {x.Name: x for x in sfapi.get_all(sf, sfapi.AccountRecordType)}

    if importtype == 'advertising':
        crc_customer_record_type = sfimport.RECORD_TYPE_CRCS_CUSTOMER_ADVERTISING
    elif importtype == 'manufacturing':
        crc_customer_record_type = sfimport.RECORD_TYPE_CRCS_CUSTOMER_MANUFACTURING
    else:
        raise Exception(f'Invalid import type {importtype}')

    SF_CONTACT_SENIORITY = sfapi.get_picklist_values(sf, 'Contact', 'Seniority__c')

    # load everything outta salesforce into local ram cache
    print("POPULATING CACHE")
    for objname in sfapi.SFAPI_OBJECT_NAMES + ['SurveyTemplateDefinition']:
        sfobject = getattr(sfapi, objname)
        sfapi.add_items_to_cache(sfimport.sfid_mapping, sfimport.sfcache, sfapi.get_all(sf, sfobject))
    print("DONE POPULATING CACHE")

    customers_client_accounts_by_account_name = {}
    for account in sfimport.sfcache.values():
        if not isinstance(account, sfapi.Account):
            continue
        if account.RecordTypeId != accountRecordTypes[sfimport.RECORD_TYPE_CUSTOMERS_CLIENT_ACCOUNT].Id:
            continue
        customers_client_accounts_by_account_name.setdefault(account.Name, []).append(account)

    crc_customer_accounts_by_account_name = {}
    for account in sfimport.sfcache.values():
        if not isinstance(account, sfapi.Account):
            continue
        if account.RecordTypeId != accountRecordTypes[crc_customer_record_type].Id:
            continue
        crc_customer_accounts_by_account_name.setdefault(account.Name, []).append(account)

    crc_customer_accounts_by_id = {}
    for account in sfimport.sfcache.values():
        if not isinstance(account, sfapi.Account):
            continue
        if account.RecordTypeId != accountRecordTypes[crc_customer_record_type].Id:
            continue
        crc_customer_accounts_by_id.setdefault(account.Id, []).append(account)

    all_contacts_by_email = {}
    for contact in sfimport.sfcache.values():
        if not isinstance(contact, sfapi.Contact):
            continue
        all_contacts_by_email.setdefault(contact.Email, []).append(contact)

    barometer_survey_template = None
    for template in sfimport.sfcache.values():
        if not isinstance(template, sfapi.SurveyTemplateDefinition):
            continue
        if template.Name == 'barometer':
            barometer_survey_template = template
            break
    if not barometer_survey_template:
        raise Exception('Could not find barometer survey template')

    # track bad rows in a separate file
    duff_file = open(f'{os.path.basename(filename)}.duffrecords-barometer.csv', 'w')
    field_names = list(all_rows[0].keys())
    duff_writer = csv.DictWriter(duff_file, fieldnames=field_names)
    duff_writer.writeheader()

    survey_type = 'Barometer'
    csr_state = 'Panel Agreed'
    csr_stage = 'Survey Setup'
    customer_survey_state = 'Panel Agreed'
    customer_survey_stage = 'Survey Setup'
    survey_client_state = 'Panel Agreed'
    # now, process each data row and generate the salesforce objects
    print('PROCESSING ROWS')
    for row in all_rows[1:]:

        # lookup crc's customer
        office_name = row['office'].strip() if row['office'] else ''
        customer_agency_leaf_account = crc_customer_accounts_by_account_name.get(office_name)
        if customer_agency_leaf_account and len(customer_agency_leaf_account) > 1:
            write_duff_row(duff_writer, row, 'MULTIPLE MATCHED CRC CUSTOMER ACCOUNTS')
            continue
        if customer_agency_leaf_account:
            customer_agency_leaf_account = customer_agency_leaf_account[0]
        if not customer_agency_leaf_account:
            write_duff_row(duff_writer, row, 'MISSING CRC CUSTOMER LEAF ACCOUNT')
            continue
        # check it is office level
        if customer_agency_leaf_account.OrganisationalLevel not in  {'Office', 'Site'}:
            write_duff_row(duff_writer, row, 'INVALID CRC CUSTOMER ORGANISATION LEVEL')
            continue

        # now find the ultimate parent account
        customer_holding_group_account = crc_customer_accounts_by_id.get(customer_agency_leaf_account.UltimateParent)
        if customer_holding_group_account and len(customer_holding_group_account) > 1:
            write_duff_row(duff_writer, row, f'MULTIPLE MATCHED ULTIMATE ACCOUNTS {customer_agency_leaf_account.UltimateParent}')
            continue
        if customer_holding_group_account:
            customer_holding_group_account = customer_holding_group_account[0]
        if not customer_holding_group_account:
            write_duff_row(duff_writer, row, f'MISSING CRC CUSTOMER ULTIMATE PARENT {customer_agency_leaf_account.UltimateParent}')
            continue
        if customer_holding_group_account.OrganisationalLevel not in {'Holding Group', 'Company'}:
            write_duff_row(duff_writer, row, 'INVALID CRC CUSTOMER ORGANISATION LEVEL FOR ULTIMATE PARENT ACCOUNT')
            continue

        # =======================================================
        # get the customer's client name
        account_name = row['account'].strip() if row['account'] else None
        if not account_name:
            write_duff_row(duff_writer, row, 'MISSING CUSTOMER CLIENT NAME')
            continue

        # =======================================================
        # job title
        contact_job_title = row['job title'].strip() if row['job title'] else None
        if contact_job_title in {'No Title Provided'}:
            contact_job_title = None
        if contact_job_title:
            contact_job_title = contact_job_title[:127] # truncate madness - field length is 128 chars

        # =======================================================
        # name
        contact_first_name = row['contacts first name'].strip() if row['contacts first name'] else ''
        contact_last_name = row['contacts last name'].strip() if row['contacts last name'] else ''

        # =======================================================
        # email address
        contact_unsafe_email_address = row['contacts email'].strip().replace('\u200b', '') if row['contacts email'] else ''
        contact_safe_email_address = sfimport.make_safe_email(contact_unsafe_email_address)

        # validate emails
        if contact_unsafe_email_address == '' or contact_first_name == '' or contact_last_name == '' or ' ' in contact_unsafe_email_address:
            row['reason']= 'MISSING EMAIL ADDRESS/ FIRST NAME/ LAST NAME'
            duff_writer.writerow(row)
            continue

        email_invalid = '@' not in contact_unsafe_email_address
        if email_invalid:
            row['reason']= 'INVALID EMAIL ADDRESS'
            duff_writer.writerow(row)
            continue
        email_chars_len = len(contact_unsafe_email_address)
        email_bytes_len = len(contact_unsafe_email_address.encode('utf-8'))
        if email_chars_len != email_bytes_len:
            row['reason']= 'INVALID EMAIL ADDRESS WITH NON-ASCII CHARACTERS'
            duff_writer.writerow(row)
            continue

        # =======================================================
        contact_seniority = row['seniority'].strip().upper() if row['seniority'] else ''
        if contact_seniority.startswith('S'):
            contact_seniority = 'Senior'
        elif contact_seniority.startswith('J'):
            contact_seniority = 'Junior'
        elif contact_seniority.startswith('M'):
            contact_seniority = 'Middle'
        elif not contact_seniority:
            contact_seniority = None
        else:
            write_duff_row(duff_writer, row, 'UNKNOWN SENIORITY')
            continue
        if contact_seniority and contact_seniority not in SF_CONTACT_SENIORITY:
            write_duff_row(duff_writer, row, 'INVALID CONTACT SENIORITY')
            continue

        # =======================================================
        # get division
        contact_legacy_division = row['division'].strip() if row['division'] else None
        if contact_legacy_division in sfimport.EXCLUDED_LEGACY_DIVISIONS:
            contact_legacy_division = None

        # =======================================================
        # TODO department

        # =======================================================
        #  TODO account tenure

        # =======================================================
        # get the survey name
        survey_name = row['survey name'].strip() if row['survey name'] else None
        if not survey_name:
            write_duff_row(duff_writer, row, 'MISSING SURVEY NAME')
            continue
        account_label = None

        # =======================================================
        # CRC Customer's Client Account
        # lookup the customer client account BY NAME
        customers_client_account = None
        if account_name:
            customers_client_account = customers_client_accounts_by_account_name.get(account_name)
            if customers_client_account and len(customers_client_account) > 1:
                write_duff_row(duff_writer, row, 'MULTIPLE MATCHED CLIENT ACCOUNTS')
                continue

            if customers_client_account:
                customers_client_account = customers_client_account[0]
        if not customers_client_account:
            write_duff_row(duff_writer, row, 'MISSING CUSTOMER CLIENT ACCOUNT')
            continue

        # =======================================================
        # signatory
        signatory_unsafe_email_address = row['signatory'].strip().replace('\u200b', '') if row['signatory'] else ''
        signatory_safe_email_address = sfimport.make_safe_email(signatory_unsafe_email_address)
        if safeemails and not signatory_unsafe_email_address.endswith('@clientrelationship.com'):
            signatory_email = signatory_safe_email_address
        else:
            signatory_email = signatory_unsafe_email_address
        resolved_signatory = all_contacts_by_email.get(signatory_email.lower())
        if resolved_signatory and len(resolved_signatory) > 1:
            write_duff_row(duff_writer, row, 'MULTIPLE MATCHED SIGNATORIES')
            continue
        if resolved_signatory:
            resolved_signatory = resolved_signatory[0]
        if not resolved_signatory:
            write_duff_row(duff_writer, row, 'SIGNATORY EMAIL NOT FOUND')
            continue
        else:
            # check contact is marked as a signatory
            if not resolved_signatory.Signatory:
                write_duff_row(duff_writer, row, 'SIGNATORY CONTACT NOT MARKED AS SIGNATORY')
                continue

        # =======================================================
        # Customer Client Relationship between the two
        customer_client_relationship = sfimport.get_or_create_cached_customer_client_relationship(customer_agency_leaf_account,
                                                                                                    customers_client_account,
                                                                                                    'Client')
        # If we've not set the survey name already, set it now
        if not customer_client_relationship.SurveyName:
            customer_client_relationship.SurveyName = survey_name
        if not customer_client_relationship.AccountLabel:
            customer_client_relationship.AccountLabel = account_label

        # =======================================================
        # Survey Contact
        if safeemails and not contact_unsafe_email_address.endswith('@clientrelationship.com'):
            contact_email_to_write = contact_safe_email_address
        else:
            contact_email_to_write = contact_unsafe_email_address

        contact = sfimport.get_or_create_cached_contact(customers_client_account,
                                                            contact_first_name,
                                                            contact_last_name,
                                                            contact_email_to_write,
                                                            seniority=contact_seniority,
                                                            job_title=contact_job_title,
                                                            language='English',
                                                            legacy_division=contact_legacy_division,
                                                            )
        contact.DisableExternalCommunications = False

        # add relation between contact/customer_client_account and the customer which surveys them
        sfimport.get_or_create_cached_account_contact_relation(customer_agency_leaf_account, contact)

        # =======================================================
        # import survey data
        survey_round = sfimport.get_or_create_cached_survey_round(survey_round_date, survey_round_end_date, '', survey_type, is_imported=True)

        customer_survey_round = sfimport.get_or_create_cached_customer_survey_round(survey_round, customer_holding_group_account, csr_stage)
        customer_survey_round.State = csr_state
        customer_survey_round.LiveSurveyStartDate = survey_start_date
        customer_survey_round.LiveSurveyEndDate = survey_end_date
        customer_survey_round.DisableExternalCommunication = False
        customer_survey_round.SurveyTemplateDefinitionId = barometer_survey_template.Id

        customer_survey = sfimport.get_or_create_cached_customer_survey(customer_survey_round,
                                                                        customer_agency_leaf_account,
                                                                        customer_survey_stage)
        customer_survey.State = customer_survey_state
        customer_survey.AccountUpdatesStartDate = account_updates_start_date
        customer_survey.AccountUpdatesEndDate = account_updates_end_date
        customer_survey.PanelUpdatesStartDate = panel_updates_start_date
        customer_survey.PanelUpdatesEndDate = panel_updates_end_date
        customer_survey.LiveSurveyStartDate = survey_start_date
        customer_survey.LiveSurveyEndDate = survey_end_date
        customer_survey.LiveSurveyFirstRequest = live_survey_first_request_date
        customer_survey.LiveSurveySecondRequest = live_survey_second_request_date
        customer_survey.LiveSurveyThirdRequest = live_survey_third_request_date
        customer_survey.LiveSurveyFourthRequest = live_survey_fourth_request_date

        survey_client = sfimport.get_or_create_cached_survey_client(customer_survey,
                                                                    customers_client_account,
                                                                    survey_client_state,
                                                                    survey_name=survey_name)
        survey_client.SignatoryId = resolved_signatory.Id

        survey_panel_member = sfimport.get_or_create_cached_survey_panel_member(survey_client,
                                                                                contact,
                                                                                hub_row_key=None)
        survey_panel_member.ContactEmail = contact_email_to_write
        survey_panel_member.ContactSeniority = contact_seniority
        survey_panel_member.ContactTitle = contact_job_title
        survey_panel_member.ContactDivision = contact_legacy_division

    # =======================================================
    # load it all into salesforce
    ok_to_write = False
    if writeit:
        debug_output(detail=False)
        if duff_row_count > 0:
            print(f"NOT writing to Salesforce. Found {duff_row_count} rows with errors, see {duff_file.name}")
        elif len(sfimport.new_accounts):
            print(f"NOT writing to Salesforce. Found {len(sfimport.new_accounts)} new accounts")
        elif len(sfimport.new_teams):
            print(f"NOT writing to Salesforce. Found {len(sfimport.new_teams)} new teams")
        elif len(sfimport.new_account_groups):
            print(f"NOT writing to Salesforce. Found {len(sfimport.new_account_groups)} new account groups")
        elif len(sfimport.new_divisions):
            print(f"NOT writing to Salesforce. Found {len(sfimport.new_divisions)} new divisions")
        elif len(sfimport.new_survey_rounds) > 1:
            print(f"NOT writing to Salesforce. Found {len(sfimport.new_survey_rounds)} new survey rounds")
        elif len(sfimport.new_customer_survey_rounds) > 1:
            print(f"NOT writing to Salesforce. Found {len(sfimport.new_customer_survey_rounds)} new customer survey rounds")
        else:
            ok_to_write = True

        if ok_to_write:
            print("Writing to Salesforce")
            print('Writing new contacts')
            sfapi.bulk_import_batch_to_sf(sf, sfimport.sfid_mapping, sfimport.sfcache, sfimport.new_contacts, sfapi.Contact)
            print('Writing new customer client relationships')
            sfapi.bulk_import_batch_to_sf(sf, sfimport.sfid_mapping, sfimport.sfcache, sfimport.new_customer_client_relationships, sfapi.CustomerClientRelationship)
            print('Writing new account contact relations')
            sfapi.bulk_import_batch_to_sf(sf, sfimport.sfid_mapping, sfimport.sfcache, sfimport.new_account_contact_relations, sfapi.AccountContactRelation)
            print('Writing new survey rounds')
            sfapi.bulk_import_batch_to_sf(sf, sfimport.sfid_mapping, sfimport.sfcache, sfimport.new_survey_rounds, sfapi.SurveyRound)
            print('Writing new customer survey rounds')
            sfapi.bulk_import_batch_to_sf(sf, sfimport.sfid_mapping, sfimport.sfcache, sfimport.new_customer_survey_rounds, sfapi.CustomerSurveyRound)
            print('Writing new customer surveys')
            sfimport.bulk_write_customer_surveys_to_sf(sf, sfimport.new_customer_surveys)
            print('Writing new survey clients')
            sfapi.bulk_import_batch_to_sf(sf, sfimport.sfid_mapping, sfimport.sfcache, sfimport.new_survey_clients, sfapi.SurveyClient)
            print('Writing new survey panel members')
            sfapi.bulk_import_batch_to_sf(sf, sfimport.sfid_mapping, sfimport.sfcache, sfimport.new_survey_panel_members, sfapi.SurveyPanelMember)
            print("Salesforce update complete")
    else:
        debug_output(detail=detailedoutput)
        print("Dry run, not writing to Salesforce")
        if duff_row_count > 0:
            print(f"Found {duff_row_count} rows with errors, see {duff_file.name}")
        else:
            print("No errors found")


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    ap.add_argument('filename', help='CSV or xlsx file to import')
    ap.add_argument('importtype', choices=['advertising', 'manufacturing'], help='Import type')
    ap.add_argument('surveystartdate', type=str, help='Survey start date YYYY-MM-DD')
    ap.add_argument('surveyenddate', type=str, help='Survey end date YYYY-MM-DD')
    ap.add_argument('--nosafeemails', action='store_false', default=True, dest='safeemails', help='Do not make email addresses safe')
    ap.add_argument('--writeit', action='store_true', default=False, dest='writeit', help='Actually write to Salesforce')
    ap.add_argument('--detailedoutput', action='store_true', default=False, dest='detailedoutput', help='Output detil of each record that would be written to SF')
    args = ap.parse_args()

    if os.environ.get('STACK') in {'prod'} and args.safeemails:
        raise Exception("Refusing to run in production with safe emails")
    if os.environ.get('STACK') not in {'prod'} and not args.safeemails:
        raise Exception("Refusing to run without safe emails")

    import_data(args.filename, args.importtype, args.surveystartdate, args.surveyenddate, args.safeemails, args.writeit, args.detailedoutput)
