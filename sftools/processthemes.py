import argparse
import pandas as pd
import os
import json
from simple_salesforce import format_soql
from lib import sfapi
from lib.settings import settings

def main(input_csv, writeit):
    try:
        print("Connecting to Salesforce...")
        sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
        
        try:
            test_query = sf.query("SELECT Id FROM Survey_Response__c LIMIT 1")
            print("Salesforce connection successful")
        except Exception as e:
            print(f"Salesforce connection test failed: {e}")
            return
        
        total_count = 0
        processed_count = 0
        error_count = 0
        update_count = 0
        
        try:
            df = pd.read_csv(input_csv)
            print(f"Loaded CSV with columns: {df.columns.tolist()}")
            
            required_columns = ['response_id', 'themes__c']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                raise Exception(f"Missing required columns: {', '.join(missing_columns)}")
            
            print("Fetching current Salesforce data...")
            soql = """
            SELECT Id, Themes__c 
            FROM Survey_Response__c
            WHERE Id IN {response_ids}
            """
            
            response_ids = tuple(df['response_id'].unique())
            if not response_ids:
                raise Exception("No response IDs found in CSV")
                
            soql = format_soql(soql, response_ids=response_ids)
            print(f"Executing SOQL: {soql}")
            
            existing_themes = {}
            for record in sf.query_all_iter(soql):
                existing_themes[record['Id']] = record['Themes__c']
            
            print(f"Found {len(existing_themes)} existing records in Salesforce")
            
            csv_ids = set(df['response_id'].unique())
            sf_ids = set(existing_themes.keys())
            missing_ids = csv_ids - sf_ids
            
            if missing_ids:
                raise Exception(f"The following IDs from CSV do not exist in Salesforce: {', '.join(sorted(missing_ids))}")
            
            print("Processing rows...")
            for index, row in df.iterrows():
                total_count += 1
                
                try:
                    sf_id = row['response_id']
                    new_themes = row['themes__c']
                    themes_json = json.loads(new_themes)
                    
                    current_themes = existing_themes.get(sf_id)
                    if current_themes == new_themes:
                        continue
                    
                    processed_count += 1
                    
                    if writeit:
                        try:
                            print(f"Updating SF record {sf_id}")
                            result = sf.Survey_Response__c.update(sf_id, {'Themes__c': new_themes})
                            update_count += 1
                            print(f"Successfully updated {sf_id} ({len(themes_json)} themes)")
                        except Exception as e:
                            print(f"Error updating {sf_id}: {e}")
                            error_count += 1
                    else:
                        print(f"[DRY RUN] Would update {sf_id} ({len(themes_json)} themes)")
                        
                except json.JSONDecodeError as e:
                    print(f"Error decoding JSON for {sf_id}: {e}")
                    error_count += 1
                except Exception as e:
                    print(f"Error processing row {index}: {e}")
                    error_count += 1
                    
        except Exception as e:
            print(f"Error processing CSV: {e}")
            return
        
        print(f'\nSummary:')
        print(f'Total records found: {total_count}')
        print(f'Records needing update: {processed_count}')
        if writeit:
            print(f'Successfully updated: {update_count}')
        print(f'Errors encountered: {error_count}')
            
    except Exception as e:
        print(f"Fatal error: {e}")
        return

if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    ap.add_argument('input_csv', help='Path to CSV file with theme data')
    ap.add_argument('--writeit', action='store_true', help='Actually write to Salesforce')
    args = ap.parse_args()
    
    if os.environ.get('STACK') not in {'prod', 'dev'}:
        raise Exception("STACK environment variable must be set to 'prod' or 'dev'")
    
    main(args.input_csv, args.writeit)