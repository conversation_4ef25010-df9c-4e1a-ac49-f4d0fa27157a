import json
import datetime
import argparse
from typing import List
from pymongo import UpdateOne, DeleteMany
from json.decoder import <PERSON><PERSON>NDecodeError
from lib import sfapi, portaldbapi, sfimport
from lib.settings import settings
from simple_salesforce import format_soql
from api.clientarea.src.models.collections import Market
from api.clientarea.src.services.markets import MarketsService


def format_round_name(round_date: str) -> str:
    return round_date.strftime('%B %Y')


def calculate_team_market_group(team_market_type: str) -> str:
    if team_market_type in ['Country', None]:
        return 'Local'
    elif team_market_type in ['Business Region', 'Key Region', 'Region']:
        return 'Regional'
    elif team_market_type in ['Global']:
        return 'Global'


def nullempty(s):
    return s if s else None


def boolify(s):
    return s in {'true', 'True', 'TRUE', '1', 1, True}


def floatify(s):
    if s in {None, ''}:
        return None
    return float(s)


def dateify(s):
    if s in {None, ''}:
        return None

    # mash it into EXACTLY the format we had before bulking
    s = datetime.datetime.fromisoformat(s).isoformat(timespec='milliseconds')
    s = s.replace('+00:00', '+0000')
    return s


def generate_client_organisation_level_lookup(sf, account_forest_by_id: dict) -> dict:
    print('>> PROCESSING ORGANISATION LEVEL LOOKUP')

    # get all markets
    market_by_id = {}
    for market in sfapi.get_all(sf, sfapi.Market, bulk=True):
        market_by_id[market.Id] = market

    # Iterate over each leaf account figuring out its hierarchy structure
    result = {}
    for account_deets in account_forest_by_id.values():
        leaf_account: sfapi.Account = account_deets['account']
        if leaf_account.CalculatedOrganisationalLevel not in {'Level 6', 'Level 7'}: # FIXME: bodge during office transition
            continue

        office = {}
        with_market = {}
        agency_brand_sub_1 = {}
        agency_brand = {}
        sub_network = {}
        network = {}
        holding_group = {}

        # Traverse up the account parent hierarchy
        cur_account = leaf_account
        while cur_account is not None:
            market_name = market_type = None
            if cur_account.MarketId:
                market: sfapi.Market = market_by_id[cur_account.MarketId]
                market_name = market.Name
                market_type = market.MarketType

            obj = {
                'id': cur_account.Id,
                'name': cur_account.Name,
                'industry': cur_account.Industry,
                'supersector': cur_account.Supersector,
                'sector': cur_account.Sector,
                'subsector': cur_account.Subsector,
                'market_id': cur_account.MarketId,
                'market_name': market_name,
                'market_type': market_type,
            }

            if cur_account.CalculatedOrganisationalLevel == 'Level 7':
                if not office:
                    office = obj
            elif cur_account.CalculatedOrganisationalLevel == 'Level 6':
                if not with_market:
                    with_market = obj
            elif cur_account.CalculatedOrganisationalLevel == 'Level 5':
                if not agency_brand_sub_1:
                    agency_brand_sub_1 = obj
            elif cur_account.CalculatedOrganisationalLevel == 'Level 4':
                if not agency_brand:
                    agency_brand = obj
            elif cur_account.CalculatedOrganisationalLevel == 'Level 3':
                if not sub_network:
                    sub_network = obj
            elif cur_account.CalculatedOrganisationalLevel == 'Level 2':
                if not network:
                    network = obj
            elif cur_account.CalculatedOrganisationalLevel == 'Level 1':
                if not holding_group:
                    holding_group = obj
            else:
                raise ValueError(f'Unknown organisational level: {cur_account.CalculatedOrganisationalLevel}')

            if cur_account.ParentId:
                cur_account: sfapi.Account = account_forest_by_id[cur_account.ParentId]['account']
            else:
                cur_account = None

        # FIXME: bodge during office transition
        if not office:
            office = with_market

        o = {
            'office': office,
            'with_market': with_market,
            'agency_brand_sub_1': agency_brand_sub_1,
            'agency_brand': agency_brand,
            'sub_network': sub_network,
            'network': network,
            'holding_group': holding_group,
        }
        result[leaf_account.Id] = o

    return result


def generate_survey_round_name_lookup(sf):
    print('>> PROCESSING SURVEY ROUNDS NAME LOOKUP')

    customer_survey_round_start_dates = {}
    customer_survey_round_labels = {}

    soql = """
    SELECT Id,
           Live_Survey_Start_Date__c,
           Customer_Survey_Round__r.Id,
           Customer_Survey_Round__r.Live_Survey_Start_Date__c
    FROM Customer_Survey__c
    """

    for customer_survey in sfapi.bulk_query(sf, soql):
        customer_survey_round_id = customer_survey['Customer_Survey_Round__r.Id']
        customer_survey_round_live_survey_start_date = customer_survey['Customer_Survey_Round__r.Live_Survey_Start_Date__c']
        customer_survey_live_survey_start_date = customer_survey['Live_Survey_Start_Date__c']

        # if whatever reason we don't have a CSR, skip
        if not customer_survey_round_id:
            continue

        # if this is the first time we're seeing this CSR, add it to the dict
        if customer_survey_round_id not in customer_survey_round_start_dates:
            customer_survey_round_start_dates[customer_survey_round_id] = []

        # if the duplication down to the CS in SF hasn't worked, we can default to the CSR date
        if not customer_survey_live_survey_start_date:
            customer_survey_live_survey_start_date = customer_survey_round_live_survey_start_date

        # if have a date, add it to the list
        if customer_survey_live_survey_start_date:
            customer_survey_round_start_dates[customer_survey_round_id].append(customer_survey_live_survey_start_date)

    for customer_survey_round_id, dates in customer_survey_round_start_dates.items():
        # if we have no dates, skip
        if not dates:
            continue

        # Parse the date strings into datetime objects
        parsed_dates = [datetime.datetime.strptime(date_str, '%Y-%m-%d') for date_str in dates]

        # Find the earliest date
        earliest_date = min(parsed_dates)

        # Get the full month name of the earliest date
        earliest_month_name = format_round_name(earliest_date)

        customer_survey_round_labels[customer_survey_round_id] = earliest_month_name

    return customer_survey_round_labels


def propagate_customer_survey_rounds(account_forest_by_id, account_id):
    # find the details
    account_deets = account_forest_by_id[account_id]
    survey_rounds_trr = account_deets.get('customer_survey_rounds_trr', {})
    survey_rounds_barometer = account_deets.get('customer_survey_rounds_barometer', {})

    # propagate up from children
    for child_account in account_deets.get('children', []):
        child_account_deets = account_forest_by_id[child_account.Id]
        tmp_trr, tmp_barometer = propagate_customer_survey_rounds(account_forest_by_id, child_account_deets['account'].Id)
        survey_rounds_trr.update(tmp_trr)
        survey_rounds_barometer.update(tmp_barometer)

    # generate a lookup of survey rounds
    sorted_survey_rounds_trr = sorted(survey_rounds_trr.items(), key=lambda x: x[1], reverse=True)
    sorted_survey_rounds_barometer = sorted(survey_rounds_barometer.items(), key=lambda x: x[1], reverse=True)
    account_deets['customer_survey_rounds_trr'] = {x[0]: {'start_date': x[1], 'index': index} for index, x in enumerate(sorted_survey_rounds_trr)}
    account_deets['customer_survey_rounds_barometer'] = {x[0]: {'start_date': x[1], 'index': index} for index, x in enumerate(sorted_survey_rounds_barometer)}

    return survey_rounds_trr, survey_rounds_barometer


def generate_survey_rounds_index_lookup(sf):
    print('>> PROCESSING SURVEY ROUNDS INDEX')

    # load all agency accounts into cache and generate account forest
    soql = format_soql("where RecordType.Name IN {record_type}", record_type=(sfimport.RECORD_TYPE_CRCS_CUSTOMER_ADVERTISING, sfimport.RECORD_TYPE_CRCS_CUSTOMER_MANUFACTURING))
    sfapi.add_items_to_cache(sfimport.sfid_mapping, sfimport.sfcache, sfapi.get_all(sf, sfapi.Account, bulk=True, query_suffix=soql))
    account_forest_roots, account_forest_by_id, account_forest_by_name = sfimport.generate_account_forest()

    # now load all customer surveys and attach their dates to the accounts
    soql = """
    SELECT Customer_Survey_Round__c,
           Customer__c,
           Customer_Survey_Round__r.Survey_Round__r.Round_Date__c,
           Survey_Type__c,
           Id
    FROM  Customer_Survey__c
    WHERE Customer__r.RecordType.Name IN {record_type}
    """
    soql = format_soql(soql, record_type=(sfimport.RECORD_TYPE_CRCS_CUSTOMER_ADVERTISING, sfimport.RECORD_TYPE_CRCS_CUSTOMER_MANUFACTURING))
    for cs in sfapi.bulk_query(sf, soql):
        customer_survey_round_id = cs['Customer_Survey_Round__c']
        agency_account_id = cs['Customer__c']
        round_start_date = cs['Customer_Survey_Round__r.Survey_Round__r.Round_Date__c']
        survey_type = cs['Survey_Type__c']

        if survey_type == 'TRR':
            account_forest_by_id[agency_account_id].setdefault('customer_survey_rounds_trr', {})[customer_survey_round_id] = round_start_date
        elif survey_type == 'Barometer':
            account_forest_by_id[agency_account_id].setdefault('customer_survey_rounds_barometer', {})[customer_survey_round_id] = round_start_date
        else:
            ValueError(f'Unknown survey type: {survey_type} for customer survey {cs.Id}')

    # now, propagate the customer survey rounds up the tree and finalise the lists of customer survey rounds
    for root_acount in account_forest_roots:
        propagate_customer_survey_rounds(account_forest_by_id, root_acount.Id)

    return account_forest_roots, account_forest_by_id, account_forest_by_name


def get_responses_by_survey_panel_member(sf,
                                         customer_survey_round_id:list|None = None,
                                         survey_panel_member_id:str|None = None,
                                         forceall:bool = False):

    filters = []
    if not forceall:
        filters.append("Survey_Panel_Member__r.LastModifiedDate >= YESTERDAY")
    if customer_survey_round_id:
        filters.append("Survey_Panel_Member__r.Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Id IN {customer_survey_round_id}")
    if survey_panel_member_id:
        filters.append("Survey_Panel_Member__r.Id = {survey_panel_member_id}")
    if filters:
        soql_filters = " WHERE " + " AND ".join(filters)
    else:
        soql_filters = ""

    soql = """
    SELECT  Id,
            Score__c,
            Score_Question__c,
            Score_Question_EN__c,
            Feedback__c,
            Feedback_Translated__c,
            Feedback_Question__c,
            Feedback_Question_EN__c,
            Themes__c,
            Is_Extra_Question__c,
            Response_Date_Time__c,
            Survey_Panel_Member__c
    FROM Survey_Response__c
    """ + soql_filters

    query = format_soql(soql, customer_survey_round_id = customer_survey_round_id, survey_panel_member_id = survey_panel_member_id)

    responses_by_survey_panel_member = {}
    for sr in sfapi.bulk_query(sf, query):
        sr['Score__c'] = floatify(sr['Score__c'])
        sr['Score_Question__c'] = nullempty(sr['Score_Question__c'])
        sr['Score_Question_EN__c'] = nullempty(sr['Score_Question_EN__c'])
        sr['Feedback__c'] = nullempty(sr['Feedback__c'])
        sr['Feedback_Translated__c'] = nullempty(sr['Feedback_Translated__c'])
        sr['Feedback_Question__c'] = nullempty(sr['Feedback_Question__c'])
        sr['Feedback_Question_EN__c'] = nullempty(sr['Feedback_Question_EN__c'])
        sr['Themes__c'] = nullempty(sr['Themes__c'])
        sr['Is_Extra_Question__c'] = boolify(sr['Is_Extra_Question__c'])
        sr['Response_Date_Time__c'] = dateify(sr['Response_Date_Time__c'])

        responses_by_survey_panel_member.setdefault(sr['Survey_Panel_Member__c'], []).append(sr)

    return responses_by_survey_panel_member


def delete_responses(
    portaldb_collection, 
    customer_survey_round_id: list | None,
    survey_panel_member_id: str | None,
    forceall: bool,
    tracked_ids: set,
    writeit: bool
):
    """ Delete responses for panel members that are no longer in SF. Only runs in forceall mode.
    """
    query = {}
    filters = []
    if not forceall:
        print(f">> SKIPPING PROCESSING OF DELETED SURVEY PANEL MEMBERS: deletion is only done in forceall mode")
        return
    print('>> PROCESSING DELETED SURVEY PANEL MEMBERS')
    if customer_survey_round_id:
        filters.append({'customer_survey_round_id': {'$in': customer_survey_round_id}})
    if survey_panel_member_id:
        filters.append({'survey_panel_member_id': survey_panel_member_id})
    if filters:
        query = {'$and': filters}

    all_ids = portaldb_collection.distinct('Id', query)
    all_ids = set(list(all_ids))
    delete_ids = all_ids - tracked_ids
    print(f">> FOUND {len(delete_ids)} PANEL MEMBERS TO DELETE")
    if writeit:
        batch_ids = []
        for panel_member_id in delete_ids:
            batch_ids.append(panel_member_id)
            if len(batch_ids) > 10000:
                    portaldb_collection.delete_many({"Id": {"$in": list(batch_ids)}})
                    batch_ids = []
        if batch_ids:
            portaldb_collection.delete_many({"Id": {"$in": list(batch_ids)}})
        print(f">> DELETED RESPONSES FOR {len(delete_ids)} PANEL MEMBERS")
    else:
        for panel_member_id in delete_ids:
            print(f'Id: "{panel_member_id}"')


def write_responses_to_portaldb(portaldb_collection, update_batch, delete_batch):
    if update_batch:
        batch = update_batch + [DeleteMany({'Id': {'$in': delete_batch}, 'response_id': None})]
        portaldb_collection.bulk_write(batch)


def sync_responses(sf,
                   customer_survey_round_id:list|None = None,
                   survey_panel_member_id:str|None = None,
                   organisation_level_lookup:dict = {},
                   survey_round_name_lookup:dict = {},
                   survey_round_lookup:dict = {},
                   current_round:bool = False,
                   writeit:bool = False,
                   forceall:bool = False) -> int:
    print('>> PROCESSING RESPONSES')

    portaldb = portaldbapi.DocumentDB()
    responses_collection = portaldbapi.get_sf_collection(portaldb, 'responses')
    barometer_collection = portaldbapi.get_sf_collection(portaldb, 'responsesbarometer') 
    update_batch = []
    update_batch_barometer = []
    delete_batch = []
    delete_batch_barometer = []
    total_record_count = 0
    total_record_count_barometer = 0
    sync_date = datetime.datetime.now(tz=datetime.UTC)

    markets:List[Market] = MarketsService(portaldb).get_markets(projection={})

    responses_by_survey_panel_member = get_responses_by_survey_panel_member(sf, customer_survey_round_id, survey_panel_member_id, forceall)

    print('>> PROCESSING SURVEY PANEL MEMBERS')

    filters = []
    if not forceall:
        filters.append("LastModifiedDate >= YESTERDAY")
    if customer_survey_round_id:
        filters.append("Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Id IN {customer_survey_round_id}")
    if survey_panel_member_id:
        filters.append("Id = {survey_panel_member_id}")
    if current_round:
        filters.append("Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Current_Round__c = TRUE")
    if filters:
        soql_filters = " WHERE " + " AND ".join(filters)
    else:
        soql_filters = ""

    soql = """
    SELECT  Id,
            Contact_Email__c,
            Contact_Type__c,
            Contact_Division__c,
            Contact_Seniority__c,
            Contact_Title__c,
            Opt_Out__c,
            SurveyJsId__c,
            Contact__r.Id,
            Contact__r.Name,
            Survey_Type__c,
            Survey_Client__r.Survey_Name__c,
            Survey_Client__r.Customers_Client__c,
            Survey_Client__r.Customers_Client_Name__c,
            Survey_Client__r.Customers_Client__r.Name,
            Survey_Client__r.Customer_Survey__r.Id,
            Survey_Client__r.Customer_Survey__r.Live_Survey_Start_Date__c,
            Survey_Client__r.Customer_Survey__r.Live_Survey_End_Date__c,
            Survey_Client__r.Customer_Survey__r.Parent_Customer_Survey__c,
            Survey_Client__r.Customer_Survey__r.Insights_Start_Date__c,
            Survey_Client__r.Customer_Survey__r.Customer__r.Id,
            Survey_Client__r.Customer_Survey__r.Customer__r.Name,
            Survey_Client__r.Customer_Survey__r.Customer__r.Legacy_Sector__c,
            Survey_Client__r.Customer_Survey__r.Team__c,
            Survey_Client__r.Customer_Survey__r.Team__r.Name,
            Survey_Client__r.Customer_Survey__r.Team__r.Market__c,
            Survey_Client__r.Customer_Survey__r.Team__r.Market__r.Name,
            Survey_Client__r.Customer_Survey__r.Team__r.Market__r.Market_Type__c,
            Survey_Client__r.Customer_Survey__r.Team__r.Type__c,
            Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Id,
            Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Linked_TRR_Survey__c,
            Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Live_Survey_Start_Date__c,
            Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Live_Survey_End_Date__c,
            Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Insights_Start_Date__c,
            Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Survey_Round__r.Id,
            Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Survey_Round__r.Name,
            Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Survey_Round__r.Round_Date__c
    FROM    Survey_Panel_Member__c
    """ + soql_filters

    query = format_soql(soql, customer_survey_round_id = customer_survey_round_id, survey_panel_member_id = survey_panel_member_id)

    # keep track of the panel members we've processed from SF
    tracked_panel_member_ids = set()
    tracked_barometer_panel_member_ids = set()
    for row in sfapi.bulk_query(sf, query):
        csr_id = row['Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Id']
        linked_trr_survey_id = nullempty(row['Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Linked_TRR_Survey__c'])
        cs_id = row['Survey_Client__r.Customer_Survey__r.Id']
        sr_id = row['Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Survey_Round__r.Id']
        client_id = row['Survey_Client__r.Customer_Survey__r.Customer__r.Id']
        survey_type = row['Survey_Type__c']

        # if we're missing being attached to a customer survey round, skip
        if not sr_id or not csr_id:
            print(f"Skipping orphan survey panel member {row['Id']}")
            continue

        # if we don't know the survey type, skip
        if survey_type not in sfapi.VALID_SURVEY_TYPES:
            print(f"Skipping survey panel member {row['Id']} with unknown survey type {survey_type}")
            continue

        csr_key = 'customer_survey_rounds_trr' if survey_type == 'TRR' else 'customer_survey_rounds_barometer'
        # Get only the non-extra question response
        survey_response = responses_by_survey_panel_member.get(row['Id'], [])

        # Get the response (if we have one)
        rs = survey_response
        if not survey_response or len(survey_response) == 0:
            rs = [{
                'Id': None,
                'Score__c': None,
                'Score_Question__c': None,
                'Score_Question_EN__c': None,
                'Feedback__c': None,
                'Feedback_Translated__c': None,
                'Feedback_Question__c': None,
                'Feedback_Question_EN__c': None,
                'Themes__c': None,
                'Response_Date_Time__c': None,
                'Is_Extra_Question__c': None,
            }]

        for r in rs:
            # Get the client hierarchy
            if client_id not in organisation_level_lookup:
                print(f"Skipping survey panel member {row['Id']} response {r['Id']} with invalid client {client_id}, CS {cs_id}, CSR {csr_id}")
                continue
            agency_details = organisation_level_lookup[client_id]
            office = agency_details['office']
            with_market = agency_details['with_market']
            agency_brand_sub_1 = agency_details['agency_brand_sub_1']
            agency_brand = agency_details['agency_brand']
            sub_network = agency_details['sub_network']
            network = agency_details['network']
            holding_group = agency_details['holding_group']

            # Get account name
            account_name = nullempty(row['Survey_Client__r.Customers_Client__r.Name'])
            account_name_custom = nullempty(row['Survey_Client__r.Customers_Client_Name__c'])
            if account_name_custom:
                account_name = account_name_custom

            # Get market city and country
            market_id = office.get('market_id')
            market_name = office.get('market_name')
            market_type = office.get('market_type')
            market_country = with_market.get('market_name')
            market_country_id = with_market.get('market_id')

            # Get the market region
            try:
                key_region = MarketsService(portaldb).get_key_region_for_market(markets, market_id)
                market_region = key_region.name
                market_region_id = key_region.Id
            except ValueError:
                market_region = None
                market_region_id = None

            # Get themes (they could initially be none)
            themes = []
            if r['Themes__c']:
                try:
                    themes = json.loads(r['Themes__c'])
                except JSONDecodeError as e:
                    print(f'Error decoding themes for response {r['Id']}: {e}')
                    themes = []

            # Get theme-specific stuff
            sentiment_count = {
                'p': 0,
                'n': 0,
                'm': 0,
            }
            theme_categories = set()
            for theme in themes:
                # Gather list of relational and transactional themes
                category_value = theme['l3']
                if category_value:
                    theme_categories.add(category_value)

                # Determine response overall sentiment
                if not theme.get('pn'):
                    continue
                if int(theme['pn']) > 0:
                    sentiment_count['p'] += 1
                elif int(theme['pn']) < 0:
                    sentiment_count['n'] += 1
                else:
                    sentiment_count['m'] += -1
            theme_categories = sorted(list(theme_categories))
            sentiment = max(sentiment_count, key=sentiment_count.get)
            if sentiment_count[sentiment] == 0:
                sentiment = None

            # Get (legacy) sector
            sector = None
            if row['Survey_Client__r.Customer_Survey__r.Customer__r.Legacy_Sector__c']:
                legacy_sectors = row['Survey_Client__r.Customer_Survey__r.Customer__r.Legacy_Sector__c'].split(',')
                if len(legacy_sectors) > 0:
                    sector = legacy_sectors[0]

            # Determine survey start and end date
            survey_start_date = nullempty(row['Survey_Client__r.Customer_Survey__r.Live_Survey_Start_Date__c'])
            survey_end_date = nullempty(row['Survey_Client__r.Customer_Survey__r.Live_Survey_End_Date__c'])
            if not survey_start_date:
                survey_start_date = nullempty(row['Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Live_Survey_Start_Date__c'])
            if not survey_end_date:
                survey_end_date = nullempty(row['Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Live_Survey_End_Date__c'])

            # Get the survey round index (per organisation level)
            office_round_index = survey_round_lookup.get(office.get('id'), {}).get(csr_key, {}).get(csr_id, {}).get('index')
            with_market_round_index = survey_round_lookup.get(with_market.get('id'), {}).get(csr_key, {}).get(csr_id, {}).get('index')
            agency_brand_sub_1_round_index = survey_round_lookup.get(agency_brand_sub_1.get('id'), {}).get(csr_key, {}).get(csr_id, {}).get('index')
            agency_brand_round_index = survey_round_lookup.get(agency_brand.get('id'), {}).get(csr_key, {}).get(csr_id, {}).get('index')
            sub_network_round_index = survey_round_lookup.get(sub_network.get('id'), {}).get(csr_key, {}).get(csr_id, {}).get('index')
            network_round_index = survey_round_lookup.get(network.get('id'), {}).get(csr_key, {}).get(csr_id, {}).get('index')
            holding_group_round_index = survey_round_lookup.get(holding_group.get('id'), {}).get(csr_key, {}).get(csr_id, {}).get('index')

            # Get the round name
            round_name = survey_round_name_lookup.get(csr_id, '')
            if not round_name:
                round_name = format_round_name(datetime.datetime.strptime(row['Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Survey_Round__r.Round_Date__c'], '%Y-%m-%d'))

            # Get Insights start date
            insights_start_date = nullempty(row['Survey_Client__r.Customer_Survey__r.Insights_Start_Date__c'])
            if not insights_start_date:
                insights_start_date = nullempty(row['Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Insights_Start_Date__c'])

            # Determine if this is a "team" customer survey, mark and so, and roll-up back to the parent customer survey
            customer_survey_id = cs_id
            team_id = nullempty(row['Survey_Client__r.Customer_Survey__r.Team__c'])
            team_name = None
            team_type = None
            team_market_id = None
            team_market_name = None
            team_market_type = None
            team_market_group = None
            team_customer_survey_id = None
            if team_id:
                team_name = nullempty(row['Survey_Client__r.Customer_Survey__r.Team__r.Name'])
                team_type = nullempty(row['Survey_Client__r.Customer_Survey__r.Team__r.Type__c'])
                team_market_id = nullempty(row['Survey_Client__r.Customer_Survey__r.Team__r.Market__c'])
                team_market_name = nullempty(row['Survey_Client__r.Customer_Survey__r.Team__r.Market__r.Name'])
                team_market_type = nullempty(row['Survey_Client__r.Customer_Survey__r.Team__r.Market__r.Market_Type__c'])
                team_market_group = calculate_team_market_group(team_market_type)
                team_customer_survey_id = cs_id
                customer_survey_id = nullempty(row['Survey_Client__r.Customer_Survey__r.Parent_Customer_Survey__c'])

            # Determine the reporting role market filter fields
            market_view_filter_id = None
            market_view_filter_name = None
            market_view_filter_type = None
            account_view_filter_id = None
            account_view_filter_name = None
            account_view_filter_type = None
            if team_market_name:
                account_view_filter_id = team_market_id
                account_view_filter_name = team_market_name
                account_view_filter_type = team_market_type
            else:
                account_view_filter_id = market_id
                account_view_filter_name = market_name
                account_view_filter_type = market_type
            if team_market_name and team_market_name == market_name:
                market_view_filter_id = team_market_id
                market_view_filter_name = team_market_name
                market_view_filter_type = team_market_type
            elif team_market_name and team_market_name != market_name:
                market_view_filter_id = None
                market_view_filter_name = None
                market_view_filter_type = None
            else:
                market_view_filter_id = market_id
                market_view_filter_name = market_name
                market_view_filter_type = market_type

            response = dict(
                Id=row['Id'],
                # Location
                market_id=market_id,
                market_name=market_name,
                market_type=market_type,
                market_country=market_country,
                market_country_id=market_country_id,
                market_region=market_region,
                market_region_id=market_region_id,
                market_view_filter_id=market_view_filter_id,
                market_view_filter_name=market_view_filter_name,
                market_view_filter_type=market_view_filter_type,
                account_view_filter_id=account_view_filter_id,
                account_view_filter_name=account_view_filter_name,
                account_view_filter_type=account_view_filter_type,
                # Client
                client_id=client_id,
                client_name=nullempty(row['Survey_Client__r.Customer_Survey__r.Customer__r.Name']),
                office_id=office.get('id'),
                office_name=office.get('name'),
                with_market_id=with_market.get('id'),
                with_market_name=with_market.get('name'),
                agency_brand_sub_1_id=agency_brand_sub_1.get('id'),
                agency_brand_sub_1_name=agency_brand_sub_1.get('name'),
                agency_brand_id=agency_brand.get('id'),
                agency_brand_name=agency_brand.get('name'),
                sub_network_id=sub_network.get('id'),
                sub_network_name=sub_network.get('name'),
                network_id=network.get('id'),
                network_name=network.get('name'),
                holding_group_id=holding_group.get('id'),
                holding_group_name=holding_group.get('name'),
                agencies=[agency_brand.get('id'), agency_brand_sub_1.get('id')],
                # Sector
                sector=sector,
                # Account
                account_id=row['Survey_Client__r.Customers_Client__c'],
                account_name=account_name,
                # Contact
                contact_id=nullempty(row['Contact__r.Id']) if survey_type == 'TRR' else None,
                contact_name=nullempty(row['Contact__r.Name']) if survey_type == 'TRR' else None,
                contact_email=nullempty(row['Contact_Email__c']) if survey_type == 'TRR' else None,
                contact_type=nullempty(row['Contact_Type__c']) if survey_type == 'TRR' else None,
                contact_job_title=nullempty(row['Contact_Title__c']) if survey_type == 'TRR' else None,
                contact_division=nullempty(row['Contact_Division__c']),
                contact_seniority=nullempty(row['Contact_Seniority__c']),
                contact_opt_out=boolify(row['Opt_Out__c']),
                # Team
                team_id=team_id,
                team_name=team_name,
                team_market_id=team_market_id,
                team_market_name=team_market_name,
                team_market_type=team_market_type,
                team_market_group=team_market_group,
                team_type=team_type,
                # Survey
                survey_id=nullempty(row['SurveyJsId__c']),
                survey_name=nullempty(row['Survey_Client__r.Survey_Name__c']),
                round_id=sr_id,
                round_name=round_name,
                customer_survey_round_id=csr_id,
                customer_survey_round_name=nullempty(row['Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Survey_Round__r.Name']),
                customer_survey_id=customer_survey_id,
                linked_trr_survey_id=linked_trr_survey_id,
                team_customer_survey_id=team_customer_survey_id,
                survey_start_date=survey_start_date,
                survey_end_date=survey_end_date,
                insights_start_date=insights_start_date,
                # Panel Member
                survey_panel_member_id=row['Id'],
                # Round
                office_round_index=office_round_index,
                with_market_round_index=with_market_round_index,
                agency_brand_sub_1_round_index=agency_brand_sub_1_round_index,
                agency_brand_round_index=agency_brand_round_index,
                sub_network_round_index=sub_network_round_index,
                network_round_index=network_round_index,
                holding_group_round_index=holding_group_round_index,
                insights_date=insights_start_date,
                # Response
                responded=bool(r['Id']),
                response_id=r['Id'],
                rating=r['Score__c'],
                rating_question=r['Score_Question__c'],
                rating_question_en=r['Score_Question_EN__c'],
                feedback=r['Feedback__c'],
                feedback_translated=r['Feedback_Translated__c'],
                feedback_question=r['Feedback_Question__c'],
                feedback_question_en=r['Feedback_Question_EN__c'],
                response_date=r['Response_Date_Time__c'],
                is_extra_question=r['Is_Extra_Question__c'],
                themes=themes,
                sentiment=sentiment,
                theme_categories=theme_categories,
                # Sync Info
                _lastsynced=sync_date,
            )

            if survey_type == 'TRR':
                tracked_panel_member_ids.add(response['Id'])
                update_batch.append(UpdateOne({'Id': response['Id'], 'response_id': response['response_id']}, {'$set': response}, upsert=True))
                if (response['responded']):
                    # delete the placeholder response doc now the panel member has responded
                    delete_batch.append(response['Id'])
                total_record_count += 1
            else:
                tracked_barometer_panel_member_ids.add(response['Id'])
                update_batch_barometer.append(UpdateOne({'Id': response['Id'], 'response_id': response['response_id']}, {'$set': response}, upsert=True))
                if (response['responded']):
                    # delete the placeholder response doc now the panel member has responded
                    delete_batch_barometer.append(response['Id'])
                total_record_count_barometer += 1

            if writeit and len(update_batch) > 10000:
                write_responses_to_portaldb(responses_collection, update_batch, delete_batch)
                update_batch = []
                delete_batch = []

            if writeit and len(update_batch_barometer) > 10000:
                write_responses_to_portaldb(barometer_collection, update_batch_barometer, delete_batch_barometer)
                update_batch_barometer = []
                delete_batch_barometer = []

    if writeit:
        write_responses_to_portaldb(responses_collection, update_batch, delete_batch)
        write_responses_to_portaldb(barometer_collection, update_batch_barometer, delete_batch_barometer)

    # delete any responses for panel members that are no longer in SF
    delete_responses(responses_collection, customer_survey_round_id, survey_panel_member_id, forceall, tracked_panel_member_ids, writeit)
    delete_responses(barometer_collection, customer_survey_round_id, survey_panel_member_id, forceall, tracked_barometer_panel_member_ids, writeit)

    result = dict(
        response_count_trr=total_record_count,
        response_count_barometer=total_record_count_barometer,
        pm_count_trr=len(tracked_panel_member_ids),
        pm_count_barometer=len(tracked_barometer_panel_member_ids),
    )
    return result


def main(customer_survey_round_id:str|None = None, survey_panel_member_id:str|None = None, current_round:bool = False, writeit:bool = False, forceall:bool = False):
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)

    # turn customer_survey_round_id param (which is a comma seperated string) into a list
    if customer_survey_round_id:
        customer_survey_round_id = customer_survey_round_id.split(',')

    _, account_forest_by_id, _ = generate_survey_rounds_index_lookup(sf)

    organisation_level_lookup = generate_client_organisation_level_lookup(sf, account_forest_by_id)

    survey_round_name_lookup = generate_survey_round_name_lookup(sf)

    result = sync_responses(
        sf,
        customer_survey_round_id,
        survey_panel_member_id,
        organisation_level_lookup,
        survey_round_name_lookup,
        account_forest_by_id,
        current_round,
        writeit,
        forceall
    )

    print(f"{result['response_count_trr']} TRR RESPONSES FOR {result['pm_count_trr']} SURVEY PANEL MEMBERS SYNCED")
    print(f"{result['response_count_barometer']} BAROMETER RESPONSES FOR {result['pm_count_barometer']} SURVEY PANEL MEMBERS SYNCED")


def lambda_handler(event, context):
    main(writeit=True)


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument('--customer_survey_round_id', default=None, help='comma seperated list of CSRs to run for')
    ap.add_argument('--survey_panel_member_id', default=None, help='single SPM to run for')
    ap.add_argument('--current_round', action='store_true', help='Resync responses for the current round only')
    ap.add_argument("--writeit", action="store_true", help="Actually write to the database")
    ap.add_argument("--forceall", action="store_true", help="Resync ALL responses")
    args = ap.parse_args()

    main(customer_survey_round_id=args.customer_survey_round_id,
         survey_panel_member_id=args.survey_panel_member_id,
         current_round=args.current_round,
         writeit=args.writeit,
         forceall=args.forceall)
