import argparse
import json
from itertools import islice
from lib import sfapi, portaldbapi, sfimport
from simple_salesforce import format_soql, Salesforce
from lib.settings import settings
from pymongo import UpdateOne


SF_BATCH_SIZE = 3000


def batch_iterable(iterable, batch_size):
    iterable = iter(iterable)
    while True:
        batch = list(islice(iterable, batch_size))
        if not batch:
            break
        yield batch


def preload_sf_data(sf: Salesforce) -> tuple[dict, dict, dict]:
    # load all agency accounts into cache and generate flat account forest
    soql = format_soql("where RecordType.Name IN {record_type}", record_type=(sfimport.RECORD_TYPE_CRCS_CUSTOMER_ADVERTISING, sfimport.RECORD_TYPE_CRCS_CUSTOMER_MANUFACTURING))
    sfapi.add_items_to_cache(sfimport.sfid_mapping, sfimport.sfcache, sfapi.get_all(sf, sfapi.Account, bulk=True, query_suffix=soql))
    _, account_forest_by_id, _ = sfimport.generate_account_forest()

    return account_forest_by_id


def get_organisation_level_lookup(account: dict, account_forest_by_id: dict) -> dict:
    level_7 = {}
    level_6 = {}
    level_5 = {}
    level_4 = {}
    level_3 = {}
    level_2 = {}
    level_1 = {}

    # Traverse up the account parent hierarchy
    cur_account = account
    while cur_account is not None:
        obj = {
            'Id': cur_account.Id,
            'name': cur_account.Name,
        }

        if cur_account.CalculatedOrganisationalLevel == 'Level 7':
            if not level_7:
                level_7 = obj
        elif cur_account.CalculatedOrganisationalLevel == 'Level 6':
            if not level_6:
                level_6 = obj
        elif cur_account.CalculatedOrganisationalLevel == 'Level 5':
            if not level_5:
                level_5 = obj
        elif cur_account.CalculatedOrganisationalLevel == 'Level 4':
            if not level_4:
                level_4 = obj
        elif cur_account.CalculatedOrganisationalLevel == 'Level 3':
            if not level_3:
                level_3 = obj
        elif cur_account.CalculatedOrganisationalLevel == 'Level 2':
            if not level_2:
                level_2 = obj
        elif cur_account.CalculatedOrganisationalLevel == 'Level 1':
            if not level_1:
                level_1 = obj
        else:
            raise ValueError(f'Unknown organisational level: {cur_account.CalculatedOrganisationalLevel}')

        if cur_account.ParentId:
            cur_account: sfapi.Account = account_forest_by_id[cur_account.ParentId]['account']
        else:
            cur_account = None

    # FIXME: bodge during office transition
    if not level_7:
        level_7 = level_6

    return {
        'level_7': level_7,
        'level_6': level_6,
        'level_5': level_5,
        'level_4': level_4,
        'level_3': level_3,
        'level_2': level_2,
        'level_1': level_1,
    }


def get_survey_panel_managers_by_survey_client_id(sf: Salesforce, survey_client_ids):
    survey_panel_managers_by_survey_client_id = {}

    for batch in batch_iterable(survey_client_ids, SF_BATCH_SIZE):
        soql = format_soql("""
            SELECT  Survey_Client__c,
                    Contact__c,
                    Contact__r.Email,
                    Contact__r.FirstName,
                    Contact__r.LastName
            FROM Survey_Panel_Manager__c
            WHERE Survey_Client__c in {batch} and Contact__c != ''
            """,
            batch=batch
        )

        for row in sfapi.bulk_query(sf, soql):
            panel_manager = portaldbapi.ConfirmPanelPanelManager(
                panel_manager_id=row["Contact__c"],
                panel_manager_email=row["Contact__r.Email"],
                panel_manager_name=f"{row['Contact__r.FirstName']} {row['Contact__r.LastName']}"
            )

            survey_panel_managers_by_survey_client_id.setdefault(row["Survey_Client__c"], {})[row['Contact__c']] = panel_manager

    return survey_panel_managers_by_survey_client_id


def get_survey_account_managers_by_customer_survey_id(sf: Salesforce, customer_survey_ids):
    soql = format_soql("""
        SELECT  Customer_Survey__c,
                Account_Manager__c
        FROM Survey_Account_Manager__c
        WHERE Customer_Survey__c in {customer_survey_ids} and Account_Manager__c != ''
        """,
        customer_survey_ids=customer_survey_ids
    )

    survey_account_managers_by_customer_survey_id = {}
    for row in sfapi.bulk_query(sf, soql):
        survey_account_managers_by_customer_survey_id.setdefault(row["Customer_Survey__c"], set()).add(row["Account_Manager__c"])
    return survey_account_managers_by_customer_survey_id


def get_related_contacts_by_agency_id_and_customer_client_id(sf: Salesforce, all_agency_customer_ids):
    soql = format_soql("""
            SELECT Serial_Non_Responder__c,
                   AccountId,
                   ContactId,
                   Contact.AccountId,
                   Contact.Email,
                   Contact.FirstName,
                   Contact.LastName,
                   Contact.Contact_Type__c,
                   Contact.Seniority__c,
                   Contact.Language__c,
                   Contact.Title,
                   Contact.Job_Function__c,
                   Contact.Job_Function_Level_2__c,
                   Contact.Account_Site__r.Id,
                   Contact.Account_Site__r.Name,
                   Contact.Location__r.Id,
                   Contact.Location__r.Name,
                   Contact.Legacy_Division__c
            FROM AccountContactRelation
            WHERE AccountId in {customer_ids} 
            AND   ContactId != ''
            AND   Contact.Deceased__c = False
            """, customer_ids=all_agency_customer_ids)

    related_contacts_by_agency_id_and_customer_client_id = {}
    for row in sfapi.bulk_query(sf, soql):
        agency_id = row['AccountId']
        customer_client_id = row['Contact.AccountId']

        related_contacts_by_agency_id_and_customer_client_id.setdefault(agency_id, {}).setdefault(customer_client_id, {})[row['ContactId']] = row
    return related_contacts_by_agency_id_and_customer_client_id


def get_prior_panel_members_by_survey_client_id(sf: Salesforce, all_agency_customer_ids, survey_client_id_to_csr_id):
    # find all survey clients with the same Customers_Client__c and Customer_Survey__c.Customer__c
    # sort them by the customer survey date descending
    # then find the current survey client in the list, and return one after that if it exists. That will be the immediately prior panel by date.
    survey_client_ids = set(survey_client_id_to_csr_id.keys())

    soql = format_soql("""
        SELECT  Id,
                Customers_Client__c,
                Customer_Survey__r.Team__c,
                Customer_Survey__r.Customer__c,
                Customer_Survey__r.Customer_Survey_Round__c
        FROM Survey_Client__c
        WHERE Customer_Survey__r.Customer__c in {customer_ids}
        ORDER BY Customer_Survey__r.Live_Survey_Start_Date__c DESC, Id ASC
    """, customer_ids=all_agency_customer_ids)

    # figure out the prior round survey client for each supplied current round survey client + team
    nab_next_survey_client = {}
    prior_survey_client_ids = {}

    for row in sfapi.bulk_query(sf, soql):
        row_survey_client_id = row["Id"]
        row_round_id = row["Customer_Survey__r.Customer_Survey_Round__c"]
        row_business_key = (row["Customer_Survey__r.Customer__c"], row["Customer_Survey__r.Team__c"], row["Customers_Client__c"])

        waiting_ids = nab_next_survey_client.get(row_business_key, set())
        matched_ids = set()

        # check for current surveys that are waiting on this business key
        for current_survey_client_id in waiting_ids:
            current_round_id = survey_client_id_to_csr_id.get(current_survey_client_id)
            if current_round_id != row_round_id:
                prior_survey_client_ids.setdefault(row_survey_client_id, set()).add(current_survey_client_id)
                matched_ids.add(current_survey_client_id)

        # remove only matched survey clients from the waiting list (and clean up if empty)
        if matched_ids:
            nab_next_survey_client[row_business_key] -= matched_ids
            if not nab_next_survey_client[row_business_key]:
                del nab_next_survey_client[row_business_key]

        # if the row_survey_client is one we want, mark that we need to nab the ***next*** time we see this row's business key
        if row_survey_client_id in survey_client_ids:
            nab_next_survey_client.setdefault(row_business_key, set()).add(row_survey_client_id)

    # now we know the prior survey client ids for each this current round, get the contact ids for each panel member in the prior survey client
    prior_panel_members_by_survey_client_id = {}
    if prior_survey_client_ids:
        for batch in batch_iterable(set(prior_survey_client_ids.keys()), SF_BATCH_SIZE):
            soql = format_soql("""
                    SELECT Survey_Client__c,
                        Contact__c
                    FROM Survey_Panel_Member__c
                    WHERE Survey_Client__c in {batch} 
                    AND   Contact__c != ''
                    AND   Contact__r.Deceased__c = False
                    """, batch=batch)

            # resolve them back to the current survey client id
            for row in sfapi.bulk_query(sf, soql):
                for current_survey_client_id in prior_survey_client_ids.get(row["Survey_Client__c"], set()):
                    prior_panel_members_by_survey_client_id.setdefault(current_survey_client_id, set()).add(row['Contact__c'])
    return prior_panel_members_by_survey_client_id


def get_related_customer_clients_by_agency_id(sf: Salesforce, all_agency_customer_ids):
    soql = format_soql("""
            SELECT  Customer_Account__c,
                    Customer_Account__r.Market__c,
                    Customers_Client__c,
                    Survey_Name__c,
                    Account_Label__c,
                    Customers_Client__r.Name,
                    Survey_Panel_Manager__c,
                    Survey_Panel_Manager__r.FirstName,
                    Survey_Panel_Manager__r.LastName,
                    Survey_Panel_Manager__r.Email,
                    Signatory__c,
                    Signatory__r.FirstName,
                    Signatory__r.LastName,
                    Signatory__r.Email
            FROM Customer_Client_Relationship__c
            WHERE Relationship_Type__c = 'Client' AND Customer_Account__c in {customer_ids} AND Customers_Client__c != ''
            """, customer_ids=all_agency_customer_ids)

    related_customer_clients_by_agency_id = {}
    for row in sfapi.bulk_query(sf, soql):
        agency_id = row['Customer_Account__c']

        related_customer_clients_by_agency_id.setdefault(agency_id, {})[row['Customers_Client__c']] = row
    return related_customer_clients_by_agency_id


def nullempty(s):
    return s if s else None


def make_portal_confirm_panel(survey_client,
                              survey_panel_managers,
                              survey_account_managers,
                              prior_panel_members,
                              related_customer_clients,
                              related_contacts,
                              account_hierarchy) -> portaldbapi.ConfirmPanel:
    all_confirm_panel_members = []
    for contact in related_contacts:
        all_confirm_panel_members.append(portaldbapi.ConfirmPanelMember(
            contact_id=contact['ContactId'],
            contact_name=f"{contact['Contact.FirstName']} {contact['Contact.LastName']}",
            contact_email=nullempty(contact['Contact.Email']),
            contact_type=nullempty(contact['Contact.Contact_Type__c']),
            contact_seniority=nullempty(contact['Contact.Seniority__c']),
            contact_language=nullempty(contact['Contact.Language__c']),
            contact_job_function=nullempty(contact['Contact.Job_Function__c']),
            contact_job_function_2=nullempty(contact['Contact.Job_Function_Level_2__c']),
            contact_job_title=nullempty(contact['Contact.Title']),
            contact_division=nullempty(contact['Contact.Legacy_Division__c']),
            contact_account_site=nullempty(contact['Contact.Account_Site__r.Name']),
            contact_account_site_id=nullempty(contact['Contact.Account_Site__r.Id']),
            contact_location=nullempty(contact['Contact.Location__r.Name']),
            contact_location_id=nullempty(contact['Contact.Location__r.Id']),
            serial_non_responder=nullempty(contact['Serial_Non_Responder__c']),
            in_round=contact['ContactId'] in prior_panel_members,
            survey_name=nullempty(survey_client['Survey_Name__c']),
        ))

    # grab the CCR is it exists
    account_name = survey_client['Customers_Client__r.Name']
    customer_client_relation = None 
    for customer_client in related_customer_clients:
        if customer_client['Customers_Client__c'] == survey_client['Customers_Client__c']:
            customer_client_relation = customer_client
            break

    # grab specific CCR account label if it exists
    if customer_client_relation:
        account_label = customer_client_relation['Account_Label__c']
        if account_label:
            account_name = customer_client_relation['Account_Label__c']

    # grab the `with market` account-level market id and name
    client_market_id = nullempty(survey_client['Customer_Survey__r.Customer__r.Market__c'])
    client_market = nullempty(survey_client['Customer_Survey__r.Customer__r.Market__r.Name'])
    if survey_client['Customer_Survey__r.Customer__r.Calculated_Organisation_Level__c'] == "Level 7":
        client_market_id = nullempty(survey_client['Customer_Survey__r.Customer__r.Parent.Market__c'])
        client_market = nullempty(survey_client['Customer_Survey__r.Customer__r.Parent.Market__r.Name'])

    # grab the `with market` account-level market language
    # if we're at the lowest (office) node, use the parents (with market) market language
    client_market_language = nullempty(survey_client['Customer_Survey__r.Customer__r.Market__r.Language__c'])
    if survey_client['Customer_Survey__r.Customer__r.Calculated_Organisation_Level__c'] == "Level 7":
        client_market_language = nullempty(survey_client['Customer_Survey__r.Customer__r.Parent.Market__r.Language__c'])

    return portaldbapi.ConfirmPanel(
        Id=survey_client['Id'],
        confirm_status=False,
        panel_managers=survey_panel_managers,
        account_managers=survey_account_managers,
        panel=all_confirm_panel_members,
        client_id=survey_client['Customer_Survey__r.Customer__c'],
        client_name=survey_client['Customer_Survey__r.Customer__r.Name'],
        client_market_id=client_market_id,
        client_market=client_market,
        client_market_language=client_market_language,
        client_organisation_level=account_hierarchy,
        account_id=survey_client['Customers_Client__c'],
        account_name=account_name,
        customer_survey_id=survey_client['Customer_Survey__c'],
        customer_survey_name=survey_client['Customer_Survey__r.Name'],
        customer_survey_round_id=survey_client['Customer_Survey__r.Customer_Survey_Round__c'],
        customer_survey_round_name=survey_client['Customer_Survey__r.Customer_Survey_Round__r.Name'],
        team_id=nullempty(survey_client['Customer_Survey__r.Team__c']),
        team_name=nullempty(survey_client['Customer_Survey__r.Team__r.Name']),
        team_market=nullempty(survey_client['Customer_Survey__r.Team__r.Market__r.Name']),
        survey_name=nullempty(survey_client['Survey_Name__c']),
        accounts_confirmed_start_date=survey_client['Customer_Survey__r.Account_Updates_Start_Date__c'],
        accounts_confirmed_by_date=survey_client['Customer_Survey__r.Account_Updates_End_Date__c'],
        panel_confirmed_start_date=survey_client['Customer_Survey__r.Panel_Updates_Start_Date__c'],
        panel_confirmed_by_date=survey_client['Customer_Survey__r.Panel_Updates_End_Date__c'],
        live_survey_start_date=survey_client['Customer_Survey__r.Live_Survey_Start_Date__c'],
        live_survey_first_request_date=survey_client['Customer_Survey__r.Live_Survey_First_Request__c'],
        live_survey_second_request_date=survey_client['Customer_Survey__r.Live_Survey_Second_Request__c'],
        live_survey_third_request_date=survey_client['Customer_Survey__r.Live_Survey_Third_Request__c'],
        live_survey_fourth_request_date=survey_client['Customer_Survey__r.Live_Survey_Fourth_Request__c'],
        live_survey_end_date=survey_client['Customer_Survey__r.Live_Survey_End_Date__c'],
        sfsync_date=None,
    )


def main(debug:bool = False, writeit:bool = False):
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    portaldb = portaldbapi.DocumentDB()
    confirm_panel_collection = portaldbapi.get_sf_collection(portaldb, portaldbapi.PORTALDB_CONFIRM_PANEL_COLLECTION)
    account_forest_by_id = preload_sf_data(sf)
    debug_output = {}

    # get the list of survey clients to process this time
    soql = """
        SELECT Id,
               Survey_Name__c,
               Customers_Client__c,
               Customers_Client__r.Name,
               Customer_Survey__c,
               Customer_Survey__r.Name,
               Customer_Survey__r.Team__c,
               Customer_Survey__r.Team__r.Name,
               Customer_Survey__r.Team__r.Market__r.Name,
               Customer_Survey__r.Team__r.Market__r.Language__c,
               Customer_Survey__r.Customer__c,
               Customer_Survey__r.Customer__r.Name,
               Customer_Survey__r.Customer__r.Market__c,
               Customer_Survey__r.Customer__r.Market__r.Name,
               Customer_Survey__r.Customer__r.Market__r.Language__c,
               Customer_Survey__r.Customer__r.Calculated_Organisation_Level__c,
               Customer_Survey__r.Customer__r.Parent.Market__c,
               Customer_Survey__r.Customer__r.Parent.Market__r.Name,
               Customer_Survey__r.Customer__r.Parent.Market__r.Language__c,
               Customer_Survey__r.Customer_Survey_Round__c,
               Customer_Survey__r.Customer_Survey_Round__r.Name,
               Customer_Survey__r.Account_Updates_Start_Date__c,
               Customer_Survey__r.Account_Updates_End_Date__c,
               Customer_Survey__r.Panel_Updates_Start_Date__c,
               Customer_Survey__r.Panel_Updates_End_Date__c,
               Customer_Survey__r.Live_Survey_Start_Date__c,
               Customer_Survey__r.Live_Survey_First_Request__c,
               Customer_Survey__r.Live_Survey_Second_Request__c,
               Customer_Survey__r.Live_Survey_Third_Request__c,
               Customer_Survey__r.Live_Survey_Fourth_Request__c,
               Customer_Survey__r.Live_Survey_End_Date__c
        FROM  Survey_Client__c
        WHERE Current_Round__c = TRUE AND Customer_Survey__r.Stage__c IN ('Survey Setup', 'Live Survey', 'Insights')
    """
    survey_clients_to_process = []
    survey_client_ids = set()
    customer_survey_ids = set()
    agency_customer_ids = set()
    survey_client_id_to_csr_id = {}
    for row in sfapi.bulk_query(sf, soql):
        survey_clients_to_process.append(row)
        survey_client_ids.add(row["Id"])
        customer_survey_ids.add(row["Customer_Survey__c"])
        agency_customer_ids.add(row["Customer_Survey__r.Customer__c"])

    # early exit if there's nothing to do
    if not survey_clients_to_process:
        return
    
    survey_client_id_to_csr_id = {
        sc["Id"]: sc["Customer_Survey__r.Customer_Survey_Round__c"]
        for sc in survey_clients_to_process
    }

    # get lists of everything
    survey_panel_managers_by_survey_client_id = get_survey_panel_managers_by_survey_client_id(sf, survey_client_ids)
    survey_account_managers_by_customer_survey_id = get_survey_account_managers_by_customer_survey_id(sf, customer_survey_ids)
    related_customer_clients_by_agency_id = get_related_customer_clients_by_agency_id(sf, agency_customer_ids)
    related_contacts_by_agency_id_and_customer_client_id = get_related_contacts_by_agency_id_and_customer_client_id(sf, agency_customer_ids)
    prior_panel_members_by_survey_client_id = get_prior_panel_members_by_survey_client_id(sf, agency_customer_ids, survey_client_id_to_csr_id)

    # now process each survey client
    batch = []
    for survey_client in survey_clients_to_process:
        customer_account = account_forest_by_id[survey_client['Customer_Survey__r.Customer__c']]['account']
        panel_managers = list(survey_panel_managers_by_survey_client_id.get(survey_client['Id'], {}).values())
        account_managers = list(survey_account_managers_by_customer_survey_id.get(survey_client['Customer_Survey__c'], set()))
        prior_panel_members = prior_panel_members_by_survey_client_id.get(survey_client['Id'], set())
        related_customer_clients = list(related_customer_clients_by_agency_id.get(survey_client['Customer_Survey__r.Customer__c'], {}).values())
        related_contacts = list(related_contacts_by_agency_id_and_customer_client_id.get(survey_client['Customer_Survey__r.Customer__c'], {}).get(survey_client['Customers_Client__c'], {}).values())
        account_hierarchy = get_organisation_level_lookup(customer_account, account_forest_by_id)

        confirm_panel = make_portal_confirm_panel(survey_client,
                                                  panel_managers,
                                                  account_managers,
                                                  prior_panel_members,
                                                  related_customer_clients,
                                                  related_contacts,
                                                  account_hierarchy)

        # sort things consistently so its easier to see if they're different for debugging!
        confirm_panel.account_managers = sorted(confirm_panel.account_managers)
        confirm_panel.panel = sorted(confirm_panel.panel, key=lambda x: x.contact_id)

        # rejig for mongo insertion
        confirm_panel_dict = confirm_panel.model_dump(by_alias=True)
        set_on_insert = {'sfsync_date': None,
                         'confirm_status': False,
                         'confirmed_panel_last_save_all_panel': None,
                         'confirmed_panel_last_save_date': None,
                         'confirmed_panel_last_save_user_id': None,
                         'confirmed_panel_last_save_user_name': None,
                         'confirmed_panel_last_save_user_email': None,
                         'confirmed_panel': [],
                         'final_confirmed_panel': []}
        for zap in set_on_insert.keys():
            confirm_panel_dict.pop(zap, None)
        confirm_panel_dict.pop('Id')

        # add to batch
        batch.append(UpdateOne({"Id": confirm_panel.Id},
                                {"$set": confirm_panel_dict, '$setOnInsert': set_on_insert},
                                upsert=True))
        
        # append output for debugging
        if debug:
            json_str = confirm_panel.model_dump_json(by_alias=True, indent=2)
            debug_output[confirm_panel.Id] = json.loads(json_str)
        
    if debug and not writeit:
        with open("debug_confirm_panel.json", "w") as f:
            json.dump(debug_output, f, indent=2, sort_keys=True)

    if writeit:
        # sync to mongo!
        if batch:
            confirm_panel_collection.bulk_write(batch)

        # reset deleted flag on any confirm panels that are still in the survey client list
        confirm_panel_collection.update_many({"Id": {'$in': list(survey_client_ids)}},
                                             {"$set": {'deleted': False}},
                                             upsert=False)

        # mark any defunct confirm panels as deleted in the database
        confirm_panel_collection.update_many({"Id": {'$nin': list(survey_client_ids)}},
                                             {"$set": {'deleted': True}},
                                             upsert=False)

def lambda_handler(event, _):
    main(
        debug=False,
        writeit=True
    )


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    ap.add_argument("--debug", action="store_true", help="Write output to JSON file for debug")
    ap.add_argument("--writeit", action="store_true", help="Actually write to the database")
    args = ap.parse_args()

    main(args.debug, args.writeit)
