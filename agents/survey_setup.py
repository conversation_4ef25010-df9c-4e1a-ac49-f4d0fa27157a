import re
import argparse
import pymongo
import pymongo.collection
from simple_salesforce import Salesforce
from lib import sfapi, portaldbapi, locale as liblocale
from lib.settings import settings


# FIXME: client name here?

TEMPLATE_VALUES = [
    (re.compile(r'\{\s*firstname\s*\}', re.I), lambda x: x["Contact__r.FirstName"]),
    (re.compile(r'\{\s*lastname\s*\}', re.I), lambda x: x["Contact__r.LastName"]),
    (re.compile(r'\{\s*clientname\s*\}', re.I), lambda x: x["Survey_Client__r.Customer_Survey__r.Customer__r.Name"]),
    (re.compile(r'\{\s*accountname\s*\}', re.I), lambda x: x["Survey_Client__r.Customers_Client__r.Name"]),
    (re.compile(r'\{\s*customer_name\s*\}', re.I), lambda x: x["Survey_Name__c"]),
]

def write_batch(sf: Salesforce, survey_collection: pymongo.collection.Collection, batch: list[dict]):
    # FIXME: delete any objects in the survey collection that have the same Id

    # write the batch to the survey collection in mongodb
    result = survey_collection.insert_many(batch)
    survey_panel_members_updates = {}
    for idx, oid in enumerate(result.inserted_ids):
        doc = batch[idx]
        for question in doc['questions']:
            for panel_member in question['panel_members']:
                survey_panel_member_id = panel_member['survey_panel_member_id']

                survey_panel_members_updates[survey_panel_member_id] = {
                    "Id": survey_panel_member_id,
                    "SurveyJsId__c": oid,
                }

    # update salesforce to point to the newly created surveys
    statuses = sfapi.bulk_update(sf, "Survey_Panel_Member__c", survey_panel_members_updates.values())
    for status in statuses:
        sfapi.detect_bulk2_errors(status)

def replace_template_values(text, d):
    for regex, fn in TEMPLATE_VALUES:
        replacement = fn(d)
        # strip out any unwanted backslashes
        replacement = replacement.replace("\\", "-")
        text = regex.sub(replacement, text)
    return text


def main(writeit):
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    portaldb = portaldbapi.DocumentDB()
    survey_collection = portaldb.client[portaldbapi.PORTALDB_DATABASE][portaldbapi.PORTALDB_SURVEY_COLLECTION]

    soql = """
    SELECT  Id,
            Name,
            Survey_Template_Definition__c,
            Contact__r.Id,
            Contact__r.FirstName,
            Contact__r.LastName,
            Contact__r.Language__c,
            Survey_Client__r.Id,
            Survey_Client__r.Survey_Name__c,
            Survey_Client__r.Survey_Template_Definition__c,
            Survey_Client__r.Customers_Client__c,
            Survey_Client__r.Customers_Client__r.Name,
            Survey_Client__r.Customer_Survey__r.Id,
            Survey_Client__r.Customer_Survey__r.Customer__c,
            Survey_Client__r.Customer_Survey__r.Customer__r.Name,
            Survey_Client__r.Customer_Survey__r.Customer__r.Parent.Name,
            Survey_Client__r.Customer_Survey__r.Survey_Template_Definition__c,
            Survey_Client__r.Customer_Survey__r.Live_Survey_Start_Date__c,
            Survey_Client__r.Customer_Survey__r.Live_Survey_End_Date__c,
            Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Survey_Template_Definition__c,
            Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Account__c,
            Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Account__r.Name,
            Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Id
    FROM  Survey_Panel_Member__c
    WHERE SurveyJsId__c = null
    AND   Survey_Client__r.State__c IN ('Survey Ready', 'Survey Out')
    AND   Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Current_Round__c = True
    """

    # keep note of all the survey clients we've dealt with
    survey_client_ids = set()

    # get all the surveys we need to send, grouped by contactid

    # FIXME: need to group by ultimate parent so we have a survey per customer?

    survey_details_by_contact_and_dates = {}
    for survey_details in sfapi.bulk_query(sf, soql):
        key = '|'.join([survey_details["Contact__r.Id"],
                        survey_details["Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Account__c"],
                        survey_details["Survey_Client__r.Customer_Survey__r.Live_Survey_Start_Date__c"],
                        survey_details["Survey_Client__r.Customer_Survey__r.Live_Survey_End_Date__c"]])

        # determine survey name
        # NOTE: BLANKVALUE doesn't support object references, so need to do this in here
        survey_name = survey_details["Survey_Client__r.Survey_Name__c"]
        if not survey_name:
            survey_name = survey_details["Survey_Client__r.Customer_Survey__r.Customer__r.Name"]
        survey_details["Survey_Name__c"] = survey_name

        survey_details_by_contact_and_dates.setdefault(key, []).append(survey_details)

        # keep note of the survey clients we've dealt with so we can fix their state later
        survey_client_ids.add(survey_details["Survey_Client__r.Id"])

    # if there's no surveys to send, just return
    if not survey_details_by_contact_and_dates:
        print("No surveys to send")
        return

    # bulk load the survey templates
    survey_templates: dict[str, sfapi.SurveyTemplateDefinition] = {}
    for survey_template in sfapi.get_all(sf, sfapi.SurveyTemplateDefinition, bulk=True):
        survey_templates[survey_template.Id] = survey_template

    # for each contactid+holding group, render their survey based on the requested survey templates
    batch = []
    for key, all_survey_details_for_contact_dates in survey_details_by_contact_and_dates.items():
        # get survey dates
        dates = key.split('|')[-2:]

        # get the contact language
        locale = liblocale.get_sf_language_to_locale(all_survey_details_for_contact_dates[0]["Contact__r.Language__c"])

        survey_config = portaldbapi.SurveyConfig(
            locale=locale, 
            pages=[],
            completeText=liblocale.get_survey_translation(locale, "submit survey")
        )

        # create a survey object
        survey = portaldbapi.Survey(start_date=dates[0],
                                    end_date=dates[1],
                                    config=survey_config,
                                    questions=[],
                                    response=None,
                                    response_date=None,
                                    sfsync_date=None)

        # Now we build a multi-page survey for all sub-surveys this contact might have been sent.
        question_id = 0
        seen_questions: dict[str, portaldbapi.SurveyQuestion] = {}
        this_survey_customer_survey_round_ids = set()
        this_survey_customer_survey_ids = set()
        this_survey_survey_client_ids = set()
        this_survey_customer_ids = set()
        this_survey_customer_client_ids = set()
        for cur_survey_details in all_survey_details_for_contact_dates:
            survey.client_name = cur_survey_details["Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Account__r.Name"]
            survey.account_id = cur_survey_details["Survey_Client__r.Customers_Client__c"]

            # grab these on the way past so we can update the survey at the end
            this_survey_customer_survey_round_ids.add(cur_survey_details["Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Id"])
            this_survey_customer_survey_ids.add(cur_survey_details["Survey_Client__r.Customer_Survey__r.Id"])
            this_survey_survey_client_ids.add(cur_survey_details["Survey_Client__r.Id"])
            this_survey_customer_ids.add(cur_survey_details["Survey_Client__r.Customer_Survey__r.Customer__c"])
            this_survey_customer_client_ids.add(cur_survey_details["Survey_Client__r.Customers_Client__c"])

            survey_panel_member_id = cur_survey_details["Id"]
            survey_panel_member_name = cur_survey_details["Name"]

            # get survey name for page title
            survey_name = cur_survey_details["Survey_Name__c"]

            survey_page = portaldbapi.SurveyConfigPage(name="", title=survey_name, elements=[])
            survey.config.pages.append(survey_page)

            # find the survey template to use, obeying order of precedence
            if cur_survey_details["Survey_Template_Definition__c"]:
                survey_template = survey_templates[cur_survey_details["Survey_Template_Definition__c"]]
            elif cur_survey_details["Survey_Client__r.Survey_Template_Definition__c"]:
                survey_template = survey_templates[cur_survey_details["Survey_Client__r.Survey_Template_Definition__c"]]
            elif cur_survey_details["Survey_Client__r.Customer_Survey__r.Survey_Template_Definition__c"]:
                survey_template = survey_templates[cur_survey_details["Survey_Client__r.Customer_Survey__r.Survey_Template_Definition__c"]]
            elif cur_survey_details["Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Survey_Template_Definition__c"]:
                survey_template = survey_templates[cur_survey_details["Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Survey_Template_Definition__c"]]
            else:
                raise Exception("Unknown template for survey panel member id: " + survey_panel_member_id)

            # we assume questions are in pairs, the first being a numeric score question and the
            # second being a freetext feedback question
            locale_sf_field_format = locale.upper().replace("-", "_")
            question_texts = getattr(survey_template, locale_sf_field_format, None)
            question_texts_en = survey_template.Questions

            if not question_texts:
                question_texts = survey_template.Questions

            question_texts = question_texts.split('\n')
            question_texts_en = question_texts_en.split('\n')

            for idx in range(0, len(question_texts), 2):
                survey_panel_member = portaldbapi.SurveyQuestionPanelMember(survey_panel_member_id=survey_panel_member_id,
                                                                            survey_panel_member_name=survey_panel_member_name)

                # get text for both questions
                score_question_text = replace_template_values(question_texts[idx], cur_survey_details)
                score_question_text_en = replace_template_values(question_texts_en[idx], cur_survey_details)
                if idx+1 < len(question_texts):
                    feedback_question_text = replace_template_values(question_texts[idx+1], cur_survey_details)
                    feedback_question_text_en = replace_template_values(question_texts_en[idx+1], cur_survey_details)
                else:
                    feedback_question_text = ""
                    feedback_question_text_en = ""

                # dedupe based on question text
                both_questions = f"{score_question_text}\n{feedback_question_text}"
                if both_questions in seen_questions:
                    seen_questions[both_questions].panel_members.append(survey_panel_member)
                    continue

                score_question_id = question_id
                score_question_element = {
                    "type": "rating",
                    "isRequired": True,
                    "name": f"{score_question_id}",
                    "title": score_question_text,
                    "rateMin": 1,
                    "rateMax": 10,
                    "rateCount": 10,
                    "minRateDescription" : liblocale.get_survey_translation(locale, "low").capitalize(),
                    "maxRateDescription" : liblocale.get_survey_translation(locale, "high").capitalize(),
                    "renderAs" : "rating",
                    "rateDescriptionLocation" : "bottom"
                }
                if score_question_text:  # don't add element if there's no question text, but do everything else
                    survey_page.elements.append(score_question_element)
                question_id += 1

                feedback_question_id = question_id
                feedback_question_element = {
                    "type": "comment",
                    "name": f"{feedback_question_id}",
                    "title": feedback_question_text,
                    "rows": 7
                }
                if feedback_question_text:  # don't add element if there's no question text, but do everything else
                    survey_page.elements.append(feedback_question_element)
                question_id += 1

                question = portaldbapi.SurveyQuestion(panel_members=[survey_panel_member],
                                                      score_question=score_question_text,
                                                      score_question_en=score_question_text_en,
                                                      score_question_id=f"{score_question_id}",
                                                      feedback_question=feedback_question_text,
                                                      feedback_question_en=feedback_question_text_en,
                                                      feedback_question_id=f"{feedback_question_id}",
                                                      is_extra_question=idx >= 2)
                seen_questions[both_questions] = question

                survey.questions.append(question)

        # NOTE: intentionally in the outer loop - inner loop is building up a single survey with multiple pages
        # now, update the survey with the list of ids
        survey_dict = survey.model_dump(by_alias=True)
        survey_dict['customer_survey_round_ids'] = list(this_survey_customer_survey_round_ids)
        survey_dict['survey_client_ids'] = list(this_survey_survey_client_ids)
        survey_dict['customer_survey_ids'] = list(this_survey_customer_survey_ids)
        survey_dict['customer_ids'] = list(this_survey_customer_ids)
        survey_dict['customer_client_ids'] = list(this_survey_customer_client_ids)
        batch.append(survey_dict)
        if len(batch) > portaldbapi.MONGO_BATCH_SIZE:
            if writeit:
                write_batch(sf, survey_collection, batch)
            batch = []

    if len(batch):
        if writeit:
            write_batch(sf, survey_collection, batch)

    # update processed survey clients to indicate survey is now out
    bulk_updates = []
    for survey_client_id in survey_client_ids:
        bulk_updates.append({"Id": survey_client_id, "State__c": "Survey Out"})
    if writeit:
        for status in sfapi.bulk_update(sf, "Survey_Client__c", bulk_updates):
            sfapi.detect_bulk2_errors(status)


def lambda_handler(event, context):
    main(True)


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument("--writeit", action="store_true", help="Actually write to the database")
    args = ap.parse_args()

    main(args.writeit)
