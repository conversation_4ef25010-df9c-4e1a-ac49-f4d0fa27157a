import json
import argparse
import datetime
from lib import sfapi, portaldbapi, sfimport
from simple_salesforce import format_soql, Salesforce
from lib.settings import settings
from pymongo import UpdateOne


def preload_sf_data(sf: Salesforce) -> tuple[dict, dict, dict]:
    # load all agency accounts into cache and generate flat account forest
    soql = format_soql("where RecordType.Name IN {record_type}", record_type=(sfimport.RECORD_TYPE_CRCS_CUSTOMER_ADVERTISING, sfimport.RECORD_TYPE_CRCS_CUSTOMER_MANUFACTURING))
    sfapi.add_items_to_cache(sfimport.sfid_mapping, sfimport.sfcache, sfapi.get_all(sf, sfapi.Account, bulk=True, query_suffix=soql))
    _, account_forest_by_id, _ = sfimport.generate_account_forest()

    return account_forest_by_id


def get_organisation_level_lookup(account: dict, account_forest_by_id: dict) -> dict:
    level_7 = {}
    level_6 = {}
    level_5 = {}
    level_4 = {}
    level_3 = {}
    level_2 = {}
    level_1 = {}

    # Traverse up the account parent hierarchy
    cur_account = account
    while cur_account is not None:
        obj = {
            'Id': cur_account.Id,
            'name': cur_account.Name,
        }

        if cur_account.CalculatedOrganisationalLevel == 'Level 7':
            if not level_7:
                level_7 = obj
        elif cur_account.CalculatedOrganisationalLevel == 'Level 6':
            if not level_6:
                level_6 = obj
        elif cur_account.CalculatedOrganisationalLevel == 'Level 5':
            if not level_5:
                level_5 = obj
        elif cur_account.CalculatedOrganisationalLevel == 'Level 4':
            if not level_4:
                level_4 = obj
        elif cur_account.CalculatedOrganisationalLevel == 'Level 3':
            if not level_3:
                level_3 = obj
        elif cur_account.CalculatedOrganisationalLevel == 'Level 2':
            if not level_2:
                level_2 = obj
        elif cur_account.CalculatedOrganisationalLevel == 'Level 1':
            if not level_1:
                level_1 = obj
        else:
            raise ValueError(f'Unknown organisational level: {cur_account.CalculatedOrganisationalLevel}')

        if cur_account.ParentId:
            cur_account: sfapi.Account = account_forest_by_id[cur_account.ParentId]['account']
        else:
            cur_account = None

    # FIXME: bodge during office transition
    if not level_7:
        level_7 = level_6

    return {
        'level_7': level_7,
        'level_6': level_6,
        'level_5': level_5,
        'level_4': level_4,
        'level_3': level_3,
        'level_2': level_2,
        'level_1': level_1,
    }


def get_prior_customer_clients_by_customer_survey_id(sf: Salesforce, toplevel_agency_customer_ids, customer_survey_id_to_csr_id):
    # find all customer surveys with the same Customer__c
    # sort them by the customer survey date descending
    # then find the current customer survey in the list, and return one after that if it exists. That will be the immediately prior panel by date.
    customer_survey_ids = set(customer_survey_id_to_csr_id.keys())

    soql = format_soql("""
        SELECT  Id,
                Team__c,
                Customer__c,
                Customer_Survey_Round__c
        FROM Customer_Survey__c
        WHERE Customer_Survey_Round__r.Account__c in {customer_ids}
        ORDER BY Customer_Survey_Round__r.Live_Survey_Start_Date__c DESC
    """, customer_ids=toplevel_agency_customer_ids)

    # figure out the prior round customer survey for each supplied current round customer survey + team
    nab_next_customer_survey = {}
    prior_customer_survey_ids = {}

    for row in sfapi.bulk_query(sf, soql):
        row_customer_survey_id = row["Id"]
        row_business_key = (row["Customer__c"], row["Team__c"])
        row_round_id = row["Customer_Survey_Round__c"]
        waiting_ids = nab_next_customer_survey.get(row_business_key, set())
        matched_ids = set()

        # check for current surveys that are waiting on this business key
        for current_customer_survey_id in waiting_ids:
            current_round_id = customer_survey_id_to_csr_id[current_customer_survey_id]
            if current_round_id != row_round_id:
                prior_customer_survey_ids.setdefault(row_customer_survey_id, set()).add(current_customer_survey_id)
                matched_ids.add(current_customer_survey_id)
        
        # remove only matched customer surveys from the waiting list (and clean up if empty)
        if matched_ids:
            nab_next_customer_survey[row_business_key] -= matched_ids
            if not nab_next_customer_survey[row_business_key]:
                del nab_next_customer_survey[row_business_key]

        # if the row_survey_client is one we want, mark that we need to nab the ***next*** time we see this row's business key
        if row_customer_survey_id in customer_survey_ids:
            nab_next_customer_survey.setdefault(row_business_key, set()).add(row_customer_survey_id)

    # get the survey panel managers for each prior survey client
    spm_soql = format_soql("""
            SELECT Survey_Client__c,
                   Contact__c,
                   Contact__r.FirstName,
                   Contact__r.LastName,
                   Contact__r.Email
            FROM Survey_Panel_Manager__c
            WHERE Survey_Client__r.Customer_Survey__c in {customer_survey_ids} and Survey_Client__r.Customers_Client__c != ''
            """, customer_survey_ids=set(prior_customer_survey_ids.keys()))

    # now we know the prior customer survey ids for each this current round, get the account ids for each account in the prior customer survey
    sc_soql = format_soql("""
            SELECT Id,
                   Customer_Survey__c,
                   Customers_Client__c,
                   Survey_Name__c,
                   Signatory__c,
                   Signatory__r.FirstName,
                   Signatory__r.LastName,
                   Signatory__r.Email
            FROM Survey_Client__c
            WHERE Customer_Survey__c in {customer_survey_ids} and Customers_Client__c != ''
            """, customer_survey_ids=set(prior_customer_survey_ids.keys()))

    # resolve them back to the current customer survey id
    prior_customer_clients_by_customer_survey_id = {}
    if prior_customer_survey_ids:
        survey_panel_managers_by_prior_survey_client_id = {}
        for row in sfapi.bulk_query(sf, spm_soql):
            survey_panel_managers_by_prior_survey_client_id.setdefault(row["Survey_Client__c"], []).append(row)

        for row in sfapi.bulk_query(sf, sc_soql):
            for current_customer_survey_id in prior_customer_survey_ids.get(row["Customer_Survey__c"], set()):
                details = {
                    'Customers_Client__c': row['Customers_Client__c'],
                    'Survey_Name__c': row['Survey_Name__c'],
                    'Signatory__c': row['Signatory__c'],
                    'Signatory__r.FirstName': row['Signatory__r.FirstName'],
                    'Signatory__r.LastName': row['Signatory__r.LastName'],
                    'Signatory__r.Email': row['Signatory__r.Email'],
                    'Survey_Panel_Managers': survey_panel_managers_by_prior_survey_client_id.get(row["Id"], [])
                }
                prior_customer_clients_by_customer_survey_id.setdefault(current_customer_survey_id, {})[row['Customers_Client__c']] = details

    return prior_customer_clients_by_customer_survey_id


def get_survey_account_managers_by_customer_survey_id(sf: Salesforce, customer_survey_ids):
    soql = format_soql("""
        SELECT  Customer_Survey__c,
                Account_Manager__c,
                Account_Manager__r.Email,
                Account_Manager__r.Name
        FROM Survey_Account_Manager__c
        WHERE Customer_Survey__c in {customer_survey_ids} and Account_Manager__c != ''
        """,
        customer_survey_ids=customer_survey_ids
    )

    survey_account_managers_by_customer_survey_id = {}
    for row in sfapi.bulk_query(sf, soql):
        survey_account_managers_by_customer_survey_id.setdefault(row["Customer_Survey__c"], {})[row["Account_Manager__c"]] = row
    return survey_account_managers_by_customer_survey_id


def get_related_customer_clients_by_agency_id(sf: Salesforce, all_agency_customer_ids):
    soql = format_soql("""
            SELECT  Customer_Account__c,
                    Customer_Account__r.Market__c,
                    Customers_Client__c,
                    Survey_Name__c,
                    Account_Label__c,
                    Customers_Client__r.Name,
                    Survey_Panel_Manager__c,
                    Survey_Panel_Manager__r.FirstName,
                    Survey_Panel_Manager__r.LastName,
                    Survey_Panel_Manager__r.Email,
                    Signatory__c,
                    Signatory__r.FirstName,
                    Signatory__r.LastName,
                    Signatory__r.Email
            FROM Customer_Client_Relationship__c
            WHERE Relationship_Type__c = 'Client' AND Customer_Account__c in {customer_ids} AND Customers_Client__c != ''
            """, customer_ids=all_agency_customer_ids)

    related_customer_clients_by_agency_id = {}
    for row in sfapi.bulk_query(sf, soql):
        agency_id = row['Customer_Account__c']

        related_customer_clients_by_agency_id.setdefault(agency_id, {})[row['Customers_Client__c']] = row
    return related_customer_clients_by_agency_id


def nullempty(s):
    return s if s else None


def make_portal_confirm_accounts(customer_survey, survey_account_managers, related_accounts, prior_survey_customer_clients, account_hierarchy):
    account_managers = []
    for row in survey_account_managers:
        account_managers.append(portaldbapi.ConfirmAccountsAccountManager(
            account_manager_id=row["Account_Manager__c"],
            account_manager_email=row["Account_Manager__r.Email"],
            account_manager_name=row['Account_Manager__r.Name']
        ))

    all_confirm_accounts_account = []
    for row in related_accounts:
        prior_survey_details = prior_survey_customer_clients.get(row['Customers_Client__c'])

        # use the survey panel manager from previous survey round if we have it, or else fall back to the details from the CCR
        confirm_accounts_panel_managers = []
        if prior_survey_details and prior_survey_details.get('Survey_Panel_Managers'):
            for spm in prior_survey_details['Survey_Panel_Managers']:
                confirm_accounts_panel_managers.append(portaldbapi.ConfirmAccountsPanelManager(
                    panel_manager_id=spm['Contact__c'],
                    panel_manager_email=spm['Contact__r.Email'],
                    panel_manager_name=f"{spm['Contact__r.FirstName']} {spm['Contact__r.LastName']}",
                    panel_manager_first_name=spm['Contact__r.FirstName'],
                    panel_manager_last_name=spm['Contact__r.LastName']
                ))

        elif row['Survey_Panel_Manager__c']:
            confirm_accounts_panel_managers.append(portaldbapi.ConfirmAccountsPanelManager(
                panel_manager_id=row['Survey_Panel_Manager__c'],
                panel_manager_email=nullempty(row['Survey_Panel_Manager__r.Email']),
                panel_manager_name=f"{row['Survey_Panel_Manager__r.FirstName']} {row['Survey_Panel_Manager__r.LastName']}",
                panel_manager_first_name=nullempty(row['Survey_Panel_Manager__r.FirstName']),
                panel_manager_last_name=nullempty(row['Survey_Panel_Manager__r.LastName'])
            ))

        # use the signatory from the prior survey round if we have it, or else fall back to the details from the CCR
        if prior_survey_details and prior_survey_details.get('Signatory__c'):
            signatory_id = nullempty(prior_survey_details['Signatory__c'])
            signatory_email = nullempty(prior_survey_details['Signatory__r.Email'])
            signatory_name = nullempty(f"{prior_survey_details['Signatory__r.FirstName']} {prior_survey_details['Signatory__r.LastName']}".strip())
        else:
            signatory_id=nullempty(row['Signatory__c'])
            signatory_email=nullempty(row['Signatory__r.Email'])
            signatory_name=nullempty(f"{row['Signatory__r.FirstName']} {row['Signatory__r.LastName']}".strip())
        
        # get survey name
        survey_name = None
        if prior_survey_details and prior_survey_details.get('Survey_Name__c'):
            survey_name = nullempty(prior_survey_details['Survey_Name__c'])

        # use CCR `Account_Label__c` if available for account_name
        account_name = nullempty(row['Customers_Client__r.Name'])
        account_label = nullempty(row['Account_Label__c'])
        if account_label:
            account_name = account_label

        all_confirm_accounts_account.append(portaldbapi.ConfirmAccountsAccount(
            account_id=row['Customers_Client__c'],
            account_name=account_name,
            survey_name=survey_name,
            survey_panel_managers=confirm_accounts_panel_managers,
            signatory_id=signatory_id,
            signatory_email=signatory_email,
            signatory_name=signatory_name,
            market_id=nullempty(row['Customer_Account__r.Market__c']),
            in_round=prior_survey_details is not None))
        
    # decpyher the market based on organisation level
    client_market = customer_survey['Customer__r.Market__r.Name']
    client_market_id = customer_survey['Customer__r.Market__c']
    # if we're at the lowest (office) node, use the parents (with market) market
    if customer_survey['Customer__r.Calculated_Organisation_Level__c'] == "Level 7":
        client_market = customer_survey['Customer__r.Parent.Market__r.Name']
        client_market_id = customer_survey['Customer__r.Parent.Market__c']

    return portaldbapi.ConfirmAccounts(
        Id=customer_survey['Id'],
        account_managers=account_managers,
        survey_round=customer_survey['Customer_Survey_Round__r.Survey_Round__r.Name'],
        customer_survey_round_id=customer_survey['Customer_Survey_Round__c'],
        customer_survey_round_name=customer_survey['Customer_Survey_Round__r.Name'],
        customer_survey_name=customer_survey['Name'],

        accounts_confirmed_start_date=customer_survey['Account_Updates_Start_Date__c'],
        accounts_confirmed_by_date=customer_survey['Account_Updates_End_Date__c'],
        panel_confirmed_start_date=customer_survey['Panel_Updates_Start_Date__c'],
        panel_confirmed_by_date=customer_survey['Panel_Updates_End_Date__c'],
        live_survey_start_date=customer_survey['Live_Survey_Start_Date__c'],
        live_survey_first_request_date=customer_survey['Live_Survey_First_Request__c'],
        live_survey_second_request_date=customer_survey['Live_Survey_Second_Request__c'],
        live_survey_third_request_date=customer_survey['Live_Survey_Third_Request__c'],
        live_survey_fourth_request_date=customer_survey['Live_Survey_Fourth_Request__c'],
        live_survey_end_date=customer_survey['Live_Survey_End_Date__c'],

        client_id=customer_survey['Customer__c'],
        client_name=customer_survey['Customer__r.Name'],
        client_market=client_market,
        client_market_id=client_market_id,
        client_organisation_level=account_hierarchy,
        team_id=nullempty(customer_survey['Team__c']),
        team_name=nullempty(customer_survey['Team__r.Name']),
        team_market=nullempty(customer_survey['Team__r.Market__r.Name']),
        accounts=all_confirm_accounts_account
    )


def main(debug:bool = False, writeit:bool = False):
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    portaldb = portaldbapi.DocumentDB()
    confirm_accounts_collection = portaldbapi.get_sf_collection(portaldb, portaldbapi.PORTALDB_CONFIRM_ACCOUNTS_COLLECTION)
    account_forest_by_id = preload_sf_data(sf)
    debug_output = {}

    # get the list of customer surveys to process this time
    soql = """
        SELECT Id,
               Name,
               Team__c,
               Team__r.Name,
               Team__r.Market__r.Name,
               Customer__c,
               Customer__r.Name,
               Customer__r.Organisation_Level__c,
               Customer__r.Calculated_Organisation_Level__c,
               Customer__r.Market__c,
               Customer__r.Market__r.Name,
               Customer__r.Parent.Market__c,
               Customer__r.Parent.Market__r.Name,
               Customer_Survey_Round__c,
               Customer_Survey_Round__r.Account__c,
               Customer_Survey_Round__r.Name,
               Customer_Survey_Round__r.Survey_Round__r.Name,
               Account_Updates_Start_Date__c,
               Account_Updates_End_Date__c,
               Panel_Updates_Start_Date__c,
               Panel_Updates_End_Date__c,
               Live_Survey_Start_Date__c,
               Live_Survey_First_Request__c,
               Live_Survey_Second_Request__c,
               Live_Survey_Third_Request__c,
               Live_Survey_Fourth_Request__c,
               Live_Survey_End_Date__c
        FROM  Customer_Survey__c
        WHERE Current_Round__c = TRUE AND Stage__c IN ('Survey Setup', 'Live Survey', 'Insights')
    """
    customer_surveys_to_process = []
    customer_survey_ids = set()
    agency_customer_ids = set()
    toplevel_agency_customer_ids = set()
    customer_survey_id_to_csr_id = {}
    for row in sfapi.bulk_query(sf, soql):
        customer_surveys_to_process.append(row)
        customer_survey_ids.add(row["Id"])
        agency_customer_ids.add(row["Customer__c"])
        toplevel_agency_customer_ids.add(row["Customer_Survey_Round__r.Account__c"])

    # early exit if there's nothing to do
    if not customer_surveys_to_process:
        return
    
    # group customer surveys by their customer survey round id
    customer_survey_id_to_csr_id = {s['Id']: s['Customer_Survey_Round__c'] for s in customer_surveys_to_process}

    # get lists of everything
    survey_account_managers_by_customer_survey_id = get_survey_account_managers_by_customer_survey_id(sf, customer_survey_ids)
    related_customer_clients_by_agency_id = get_related_customer_clients_by_agency_id(sf, agency_customer_ids)
    prior_customer_clients_by_customer_survey_id = get_prior_customer_clients_by_customer_survey_id(sf, toplevel_agency_customer_ids, customer_survey_id_to_csr_id)

    # now process each customer survey
    batch = []
    for customer_survey in customer_surveys_to_process:
        customer_account = account_forest_by_id[customer_survey['Customer__c']]['account']
        account_managers = list(survey_account_managers_by_customer_survey_id.get(customer_survey['Id'], {}).values())
        prior_customer_clients = prior_customer_clients_by_customer_survey_id.get(customer_survey['Id'], {})
        related_customer_clients = list(related_customer_clients_by_agency_id.get(customer_survey['Customer__c'], {}).values())
        account_hierarchy = get_organisation_level_lookup(customer_account, account_forest_by_id)

        confirm_account = make_portal_confirm_accounts(customer_survey,
                                                       account_managers,
                                                       related_customer_clients,
                                                       prior_customer_clients,
                                                       account_hierarchy)

        # sort things consistently so its easier to see if they're different for debugging!
        confirm_account.account_managers = sorted(confirm_account.account_managers, key=lambda x: x.account_manager_id)
        confirm_account.accounts = sorted(confirm_account.accounts, key=lambda x: x.account_id)

        # rejig for mongo insertion
        confirm_account_dict = confirm_account.model_dump(by_alias=True)
        set_on_insert = {'sfsync_date': None,
                         'confirm_status': False,
                         'auto_confirmed': False,
                         'confirmed_accounts_last_save_all_accounts': None,
                         'confirmed_accounts_last_save_date': None,
                         'confirmed_accounts_last_save_user_id': None,
                         'confirmed_accounts_last_save_user_name': None,
                         'confirmed_accounts_last_save_user_email': None,
                         'confirmed_accounts': [],
                         'final_confirmed_accounts': [],
                         'delegation': []}
        for zap in set_on_insert.keys():
            confirm_account_dict.pop(zap, None)
        confirm_account_dict.pop('Id')

        # add to batch
        batch.append(UpdateOne({"Id": confirm_account.Id},
                                {"$set": confirm_account_dict, '$setOnInsert': set_on_insert},
                                upsert=True))
        
        # append output for debugging
        if debug:
            json_str = confirm_account.model_dump_json(by_alias=True, indent=2)
            debug_output[confirm_account.Id] = json.loads(json_str)
    
    # if we're debugging, write the output to a file
    if debug and not writeit:
        with open("debug_confirm_accounts.json", "w") as f:
            json.dump(debug_output, f, indent=2, sort_keys=True)

    if writeit:
        # write to portaldb
        if batch:
            confirm_accounts_collection.bulk_write(batch)

        # reset the deleted flag on any accounts that are still in the current list
        confirm_accounts_collection.update_many({"Id": {'$in': list(customer_survey_ids)}},
                                                {"$set": {'deleted': False}},
                                                upsert=False)

        # mark defunct
        confirm_accounts_collection.update_many({"Id": {'$nin': list(customer_survey_ids)}},
                                                {"$set": {'deleted': True}},
                                                upsert=False)


def lambda_handler(event, _):
    main(
        debug=False,
        writeit=True
    )


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument("--debug", action="store_true", help="Write json debug output to file")
    ap.add_argument("--writeit", action="store_true", help="Actually write to the database")
    args = ap.parse_args()

    main(args.debug, args.writeit)
