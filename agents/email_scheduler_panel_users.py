""" Schedule panel member survey emails for live survey rounds

DATA:
    FROM: SF (Customer_Survey_Round__c, Survey_Panel_Member__c)
    TO: PortalDB (scheduledemails)

"""
import argparse
import datetime
import re
import pytz
import bleach
import html
from pymongo import UpdateOne, DeleteOne
from simple_salesforce import Salesforce, format_soql
from lib.settings import settings
from lib import sfapi, portaldbapi, locale as liblocale, sfimport


SF_RESOURCE_URL = 'file.force.com'
DEFAULT_SENDER = '<EMAIL>' # TODO: move to secrets
DEFAULT_SCHEDULED_HOUR = 7
DEFAULT_SCHEDULED_MINUTE = 30


def nullempty(value):
    return value if value else None


def boolify(value):
    return value in {'true', 'True', 'TRUE', '1', 1, True}


def sanitise_html(string:str) -> str:
    if not isinstance(string, str):
        return ''

    cleaned_string:str = bleach.clean(string, tags=[], strip=True)
    return html.unescape(cleaned_string)


def calculate_scheduled_date(template_send_date:str, vertical:str, account_timezone:str|None, contact_location_timezone:str|None) -> tuple[datetime.datetime, datetime.datetime]:
    default_timezone = pytz.utc
    market_timezone = None

    template_send_date = datetime.datetime.strptime(template_send_date, "%Y-%m-%d").date()

    if vertical == 'adv':
        market_timezone = account_timezone
    if vertical == 'mfv':
        market_timezone = contact_location_timezone

    if market_timezone:
        try:
            timezone = pytz.timezone(market_timezone)
        except pytz.UnknownTimeZoneError:
            print(f'   !! WARNING: Unknown timezone {market_timezone} for market. Could not convert to pytz timezone. Using default UTC timezone.')
            timezone = default_timezone
    else:
        print('   !! WARNING: No timezone set on the market in Salesforce. Using default UTC timezone.')
        timezone = default_timezone

    naive_datetime = datetime.datetime(template_send_date.year, template_send_date.month, template_send_date.day, DEFAULT_SCHEDULED_HOUR, DEFAULT_SCHEDULED_MINUTE)

    scheduled_date_local = timezone.localize(naive_datetime)
    scheduled_date_utc = scheduled_date_local.astimezone(pytz.utc)

    return market_timezone, scheduled_date_local, scheduled_date_utc


def determine_vertical(account_record_types:dict[str, str], record_type_id:str):
    vertical:str = 'adv'
    if record_type_id == account_record_types[sfimport.RECORD_TYPE_CRCS_CUSTOMER_ADVERTISING].Id:
        vertical = 'adv'
    elif record_type_id == account_record_types[sfimport.RECORD_TYPE_CRCS_CUSTOMER_MANUFACTURING].Id:
        vertical = 'mfv'
    return vertical


def get_contact_from_spm(spm):
    contact = dict(
        Id=spm['Contact__r.Id'],
        Email=spm['Contact__r.Email'],
        Name=spm['Contact__r.Name'],
        FirstName=spm['Contact__r.FirstName'],
        LastName=spm['Contact__r.LastName'],
        Language__c=spm['Contact__r.Language__c'],
    )
    return contact


def get_customer_survey_from_spm(spm):
    customer_survey = dict(
        External_Communication_Email_Address__c=spm['Survey_Client__r.Customer_Survey__r.External_Communication_Email_Address__c'],
        Live_Survey_Start_Date__c=spm['Survey_Client__r.Customer_Survey__r.Live_Survey_Start_Date__c'],
        Live_Survey_End_Date__c=spm['Survey_Client__r.Customer_Survey__r.Live_Survey_End_Date__c'],
    )
    return customer_survey


def get_signatory_from_spm(spm):
    signatory = dict(
        Id=spm['Survey_Client__r.Signatory__r.Id'],
        Name=spm['Survey_Client__r.Signatory__r.Name'],
        Email=spm['Survey_Client__r.Signatory__r.Email'],
        Title=spm['Survey_Client__r.Signatory__r.Title'],
        Banner__c=spm['Survey_Client__r.Signatory__r.Banner__c'],
        Signature__c=spm['Survey_Client__r.Signatory__r.Signature__c'],
    )
    return signatory


def get_email_template_name(reminder_number:int, survey_type: str) -> str:
    name:str = ''
    if survey_type == 'TRR':
        name = 'live_survey_participant'
        if reminder_number == 1:
            name = 'live_survey_participant_reminder_1'
        elif reminder_number == 2:
            name = 'live_survey_participant_reminder_2'
        elif reminder_number == 3:
            name = 'live_survey_participant_reminder_3'
    elif survey_type == 'Barometer':
        name = 'live_barometer_participant'
        if reminder_number == 1:
            name = 'live_barometer_participant_reminder_1'
        elif reminder_number == 2:
            name = 'live_barometer_participant_reminder_2'
        elif reminder_number == 3:
            name = 'live_barometer_participant_reminder_3'
    else:
        raise ValueError(f'Unknown survey type: {survey_type}')

    return name


def get_email_service_provider(spm) -> str:
    # check the CSR first
    ses_checked = boolify(spm.get('Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Send_Emails_Via_SES__c'))
    if not ses_checked:
        # check the survey round
        ses_checked = boolify(spm.get('Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Survey_Round__r.Send_Emails_Via_SES__c'))
    if ses_checked:
        return 'ses'
    return 'sendgrid'


def get_scheduled_emails_from_portaldb(collection:portaldbapi.DocumentDB, customer_survey_round_ids:list[str]) -> dict:
    print(' >> FETCHING: SCHEDULED EMAILS FROM PORTALDB...')
    scheduled_emails:dict[str,dict] = {}

    query:dict = {
        'type': 'panel',
        'customer_survey_round_id': {'$in': customer_survey_round_ids},
        'sent_to_sendgrid_date': {'$exists': False},
    }
    projection:dict = {
        'contact_id': 1,
        'survey_id': 1,
        'template_name': 1,
        'sent_to_sendgrid_date': 1,
        'customer_survey_round_id': 1,
        'deleted': 1,
    }

    for doc in collection.find(query, projection):
        csr_id:str = doc['customer_survey_round_id']
        key:str = f'{doc["contact_id"]}-{doc["survey_id"]}-{doc["template_name"]}'
        is_deleted:bool = doc.get('deleted', False)
        schedule:dict = scheduled_emails.setdefault(csr_id, {}).setdefault(doc['template_name'], {'active': set(), 'deleted': set()})
        if is_deleted:
            schedule['deleted'].add(key)
        else:
            schedule['active'].add(key)

    return scheduled_emails


def group_survey_panel_members(survey_panel_members:list) -> dict:
    first_email_panel_members:list = []
    reminder_1_email_panel_members:list = []
    reminder_2_email_panel_members:list = []
    reminder_3_email_panel_members:list = []
    contact_profile_count:dict[str,set] = {}
    contact_survey_names:dict[str,set] = {}

    for spm in survey_panel_members:
        contact_id:str = spm.get('Contact__r.Id')

        # determine agency brand id based on org-level (and handle accounts that do not support office yet)
        agency_brand_id = spm.get('Survey_Client__r.Customer_Survey__r.Customer__r.Parent.Parent.Id')
        agency_brand_name = spm.get('Survey_Client__r.Customer_Survey__r.Customer__r.Parent.Parent.Name')
        if spm.get('Survey_Client__r.Customer_Survey__r.Customer__r.Calculated_Organisation_Level__c') == "Level 6": # "Level 6 == With Market"
            agency_brand_id = spm.get('Survey_Client__r.Customer_Survey__r.Customer__r.Parent.Id')
            agency_brand_name = spm.get('Survey_Client__r.Customer_Survey__r.Customer__r.Parent.Name')

        if contact_id not in contact_profile_count:
            contact_profile_count[contact_id] = set()

        contact_profile_count[contact_id].add(agency_brand_id)

        # collect survey names for each contact
        survey_name = spm.get('Survey_Client__r.Survey_Name__c')
        if not survey_name:
            survey_name = agency_brand_name
        contact_survey_names_key = f'{contact_id}-{spm.get("SurveyJsId__c")}'
        contact_survey_names.setdefault(contact_survey_names_key, set()).add(survey_name)

        # split panel members into 1st email and 1st/2nd/3rd reminder email groups
        m1_sent = boolify(spm.get('Survey_Email_Triggered__c'))
        m2_sent = boolify(spm.get('Survey_Email_Reminder_1__c'))
        m3_sent = boolify(spm.get('Survey_Email_Reminder_2__c'))
        m4_sent = boolify(spm.get('Survey_Email_Reminder_3__c'))

        if m1_sent == False:
            first_email_panel_members.append(spm)
        if m2_sent == False:
            reminder_1_email_panel_members.append(spm)
        if m3_sent == False:
            reminder_2_email_panel_members.append(spm)
        if m4_sent == False:
            reminder_3_email_panel_members.append(spm)

    print(f' >> FETCHED: LIVE SURVEY PARTICIPANTS || M1: {len(first_email_panel_members)}, M2: {len(reminder_1_email_panel_members)}, M3: {len(reminder_2_email_panel_members)}, M4: {len(reminder_3_email_panel_members)}')

    return dict(
        first_email_panel_members=first_email_panel_members,
        reminder_1_email_panel_members=reminder_1_email_panel_members,
        reminder_2_email_panel_members=reminder_2_email_panel_members,
        reminder_3_email_panel_members=reminder_3_email_panel_members,
        contact_profile_count=contact_profile_count,
        contact_survey_names=contact_survey_names,
    )


def get_panel_members_from_sf(sf:Salesforce, customer_survey_round_ids:list[str]) -> dict:
    print(' >> FETCHING: SURVEY PANEL MEMBERS FROM SF...')
    survey_panel_members_by_customer_survey_round:dict[str, list] = {}

    soql = """
    SELECT Id,
            Name,
            SurveyJsId__c,
            Contact__r.Id,
            Contact__r.Email,
            Contact__r.Name,
            Contact__r.FirstName,
            Contact__r.LastName,
            Contact__r.Language__c,
            Contact__r.Location__r.Timezone__c,
            Survey_Email_Triggered__c,
            Survey_Email_Reminder_1__c,
            Survey_Email_Reminder_2__c,
            Survey_Email_Reminder_3__c,
            Survey_Type__c,
            Survey_Client__r.Id,
            Survey_Client__r.Survey_Name__c,
            Survey_Client__r.Signatory__r.Id,
            Survey_Client__r.Signatory__r.Name,
            Survey_Client__r.Signatory__r.Email,
            Survey_Client__r.Signatory__r.Title,
            Survey_Client__r.Signatory__r.Account.Name,
            Survey_Client__r.Signatory__r.Banner__c,
            Survey_Client__r.Signatory__r.Signature__c,
            Survey_Client__r.Customers_Client__c,
            Survey_Client__r.Customer_Survey__r.Id,
            Survey_Client__r.Customer_Survey__r.External_Communication_Email_Address__c,
            Survey_Client__r.Customer_Survey__r.Live_Survey_End_Date__c,
            Survey_Client__r.Customer_Survey__r.Customer__r.RecordTypeId,
            Survey_Client__r.Customer_Survey__r.Customer__r.Market__r.Timezone__c,
            Survey_Client__r.Customer_Survey__r.Customer__r.Parent.Id,
            Survey_Client__r.Customer_Survey__r.Customer__r.Parent.Name,
            Survey_Client__r.Customer_Survey__r.Customer__r.Parent.Parent.Id,
            Survey_Client__r.Customer_Survey__r.Customer__r.Parent.Parent.Name,
            Survey_Client__r.Customer_Survey__r.Customer__r.Calculated_Organisation_Level__c,
            Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Id,
            Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Email_Live_Survey_First_Request__c,
            Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Email_Live_Survey_Second_Request__c,
            Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Email_Live_Survey_Third_Request__c,
            Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Email_Live_Survey_Fourth_Request__c,
            Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Send_Emails_Via_SES__c,
            Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Account__r.Name,
            Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Survey_Round__r.Id,
            Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Survey_Round__r.Send_Emails_Via_SES__c,
            Survey_Client__r.Customer_Survey__r.Live_Survey_First_Request__c,
            Survey_Client__r.Customer_Survey__r.Live_Survey_Second_Request__c,
            Survey_Client__r.Customer_Survey__r.Live_Survey_Third_Request__c,
            Survey_Client__r.Customer_Survey__r.Live_Survey_Fourth_Request__c,
            Survey_Client__r.Customer_Survey__r.Live_Survey_Start_Date__c
    FROM  Survey_Panel_Member__c
    WHERE SurveyJsId__c != NULL
    AND   Has_Responded__c = False
    AND   Contact__r.HasOptedOutOfEmail = False
    AND   Contact__r.Disable_External_Communication__c = False
    AND   Opt_Out__c = False
    AND   Survey_Client__r.Customer_Survey__r.Stage__c = 'Live Survey'
    AND   Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__c IN {customer_survey_round_ids}
    AND   Survey_Client__r.Customer_Survey__r.Disable_External_Communication__c = False
    AND   Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Disable_External_Communication__c = False
    AND   Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Survey_Round__r.Disable_External_Communication__c = False
    AND   (Survey_Email_Triggered__c = False OR Survey_Email_Reminder_1__c = False OR Survey_Email_Reminder_2__c = False OR Survey_Email_Reminder_3__c = False)
    """

    query:str = format_soql(soql, customer_survey_round_ids=customer_survey_round_ids)

    for spm in sfapi.bulk_query(sf, query):
        csr_id:str = spm.get('Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Id')
        survey_panel_members_by_customer_survey_round.setdefault(csr_id, []).append(spm)

    return survey_panel_members_by_customer_survey_round


def get_customer_survey_rounds_from_sf(sf:Salesforce, round_id:str|None = None) -> list:
    print(' >> FETCHING: CUSTOMER SURVEY ROUNDS FROM SF...')
    customer_survey_rounds:list = []

    soql = """
    SELECT Id,
           Name,
           Disable_External_Communication__c,
           Email_Round_Scheduled__c,
           Stage__c,
           Account_Updates_Start_Date__c,
           Account_Updates_End_Date__c,
           Panel_Updates_Start_Date__c,
           Panel_Updates_End_Date__c,
           Live_Survey_Start_Date__c,
           Live_Survey_First_Request__c,
           Live_Survey_Second_Request__c,
           Live_Survey_Third_Request__c,
           Live_Survey_Fourth_Request__c,
           Live_Survey_End_Date__c,
           Email_Live_Survey_First_Request__c,
           Email_Live_Survey_Second_Request__c,
           Email_Live_Survey_Third_Request__c,
           Email_Live_Survey_Fourth_Request__c,
           Account__r.Name,
           Account__r.Consultant__r.Email,
           Survey_Round__r.Name,
           Survey_Round__r.Disable_External_Communication__c
    FROM  Customer_Survey_Round__c
    WHERE Current_Round__c = True
    """

    if round_id:
        soql = soql + " AND Id = {round_id}"

    query:str = format_soql(soql, round_id=round_id)

    for csr in sfapi.bulk_query(sf, query):
        customer_survey_rounds.append(csr)

    return customer_survey_rounds


def process_participants(csr_id:str,
                         panel_members:list,
                         contact_profile_count:dict[str, set],
                         contact_survey_names:dict[str, set],
                         reminder_number:int,
                         account_record_types:dict[str, str],
                         scheduled_emails:dict[str, set]) -> list[UpdateOne|DeleteOne]:
    batch:list[UpdateOne] = []
    has_been_scheduled:set = set()
    scheduled_count:int = 0
    templates_processed:set = set()
    cs_property_name:str = 'Live_Survey_First_Request__c'
    spm_property_name:str = 'Survey_Email_Triggered__c'
    now:datetime.datetime = datetime.datetime.now(datetime.UTC)
    if reminder_number == 1:
        cs_property_name:str = 'Live_Survey_Second_Request__c'
        spm_property_name:str = 'Survey_Email_Reminder_1__c'
    elif reminder_number == 2:
        cs_property_name:str = 'Live_Survey_Third_Request__c'
        spm_property_name:str = 'Survey_Email_Reminder_2__c'
    elif reminder_number == 3:
        cs_property_name:str = 'Live_Survey_Fourth_Request__c'
        spm_property_name:str = 'Survey_Email_Reminder_3__c'

    for spm in panel_members:
        contact = get_contact_from_spm(spm)
        signatory = get_signatory_from_spm(spm)
        customer_survey = get_customer_survey_from_spm(spm)
        survey_type = spm.get('Survey_Type__c')
        email_template_name = get_email_template_name(reminder_number, survey_type)
        templates_processed.add(email_template_name)
        has_been_schduled_key = f'{contact.get("Id")}-{spm.get('SurveyJsId__c')}-{email_template_name}'
        recipient = contact.get('Email')
        holding_group_name = spm.get('Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Account__r.Name', '')
        vertical = determine_vertical(account_record_types, spm.get('Survey_Client__r.Customer_Survey__r.Customer__r.RecordTypeId'))
        external_communication_email = customer_survey.get('External_Communication_Email_Address__c')
        banner_attachment = None
        signature_attachment = None
        banner = signatory.get('Banner__c')
        signature = signatory.get('Signature__c')
        survey_name_key = f'{contact.get("Id")}-{spm.get('SurveyJsId__c')}'
        is_multi_profile = len(contact_profile_count.get(contact.get('Id'), [])) > 1
        sender = external_communication_email if external_communication_email else DEFAULT_SENDER
        locale = liblocale.get_sf_language_to_locale_formatted(contact.get('Language__c'))
        agency_brand_name = spm.get('Survey_Client__r.Customer_Survey__r.Customer__r.Parent.Parent.Name')
        survey_name = ', '.join(contact_survey_names.get(survey_name_key, []))
        if survey_type == 'Barometer' and len(contact_survey_names.get(survey_name_key, [])) > 1:
            # for Barometer if the PM is being surveyed for multiple surveys, the email subject is wanted as
            # "How do you rate working on your Accounts?" rather than "How do you rate working on AccountA, AccountB, AccountC?"
            survey_name = 'your Accounts'
        from_name = None
        email_service_provider = get_email_service_provider(spm)

        # skip the email if it's already been sent
        if boolify(spm.get(spm_property_name)):
            print(f'   :: Email Skipped: {email_template_name} already sent to {recipient} for Survey_Panel_Member__c {spm.get("Id")}')
            continue

        # determine agency brand name based on org-level (and handle accounts that do not support office yet)
        if spm.get('Survey_Client__r.Customer_Survey__r.Customer__r.Calculated_Organisation_Level__c') == "Level 6": # "Level 6 == With Market"
            agency_brand_name = spm.get('Survey_Client__r.Customer_Survey__r.Customer__r.Parent.Name')

        if is_multi_profile:
            agency_brand_name = holding_group_name

        # format survey names and fallback to agency brand name if there's none
        # FIXME: would prefer not to re-use `agency_brand_name` here, but requires a mass template update.
        if survey_name:
            agency_brand_name = survey_name

        if signatory and banner and SF_RESOURCE_URL in banner:
            cid = f'cid:{signatory.get("Id")}-banner'
            banner = re.sub(r'src="[^"]+"', f'src="{cid}"', banner)
            banner_attachment = {
                'disposition': 'inline',
                'key': f'{signatory.get("Id")}-banner.jpg',
                'file_type': 'image/jpeg',
                'content_id': f'{signatory.get("Id")}-banner',
            }

        if signatory and signature and SF_RESOURCE_URL in signature:
            from_name = signatory.get('Name')
            cid = f'cid:{signatory.get("Id")}'
            signature = re.sub(r'src="[^"]+"', f'src="{cid}"', signature)
            signature_attachment = {
                'disposition': 'inline',
                'key': f'{signatory.get("Id")}.jpg',
                'file_type': 'image/jpeg',
                'content_id': signatory.get('Id'),
            }

        # this is a fallback in case the signatory image is not available/missing
        if signatory and not signature:
            signature_name = '' if signatory.get('Name') == None else signatory.get('Name', '')
            signature_title = '' if signatory.get('Title') == None else signatory.get('Title', '')
            signature_account = spm.get('Survey_Client__r.Signatory__r.Account.Name') if spm.get('Survey_Client__r.Signatory__r.Account.Name') else ''
            signature_email = '' if signatory.get('Email') == None else signatory.get('Email', '')
            signature = f'<p><strong>{signature_name}</strong></p><p>{signature_title}</p><p>{signature_account}</p><p>{signature_email}</p>'
            from_name = signatory.get('Name')

        # calculate scheduled send date
        timezone, scheduled_date_local, scheduled_date_utc = calculate_scheduled_date(spm.get(f'Survey_Client__r.Customer_Survey__r.{cs_property_name}'),
                                                                                      vertical,
                                                                                      spm.get('Survey_Client__r.Customer_Survey__r.Customer__r.Market__r.Timezone__c'),
                                                                                      nullempty(spm.get('Contact__r.Location__r.Timezone__c')))

        # construct template metadata and data
        template_data = {
            'FirstName': sanitise_html(contact.get('FirstName')),
            'LastName': sanitise_html(contact.get('LastName')),
            'SurveyDate': customer_survey.get('Live_Survey_Start_Date__c'),
            'LiveSurveyEndDate': customer_survey.get('Live_Survey_End_Date__c'),
            'SurveyLink': f'{settings.survey_ui_link.rstrip('/')}/survey/{spm.get('SurveyJsId__c')}',
            'Banner': banner,
            'Signatory': signature,
            'AgencyBrand': sanitise_html(agency_brand_name),
            'HoldingGroup': sanitise_html(holding_group_name),
            'OptOutLink': f'{settings.survey_ui_link.rstrip('/')}/optout/{contact.get('Id')}',
            'PrivacyPolicyLink': f'{settings.survey_ui_link.rstrip('/')}/privacy/{spm.get('SurveyJsId__c')}',
        }
        template_metadata = {
            'contact_id': contact.get('Id'),
            'survey_panel_member_id': spm.get('Id'),
            'survey_client_id': spm.get('Survey_Client__r.Id'),
            'customer_survey_id': spm.get('Survey_Client__r.Customer_Survey__r.Id'),
            'customer_survey_round_id': csr_id,
            # this property is used to filter sendgrid email events, see: api/agents/src/handlers/sendgrid/event_handler.py
            # suffix appended in non-prod environments to prevent tracking of test emails by prod
            'tracked_template_name': email_template_name if not settings.TRACKED_TEMPLATE_SUFFIX else f'{email_template_name}_{settings.TRACKED_TEMPLATE_SUFFIX}',
        }

        if has_been_schduled_key not in has_been_scheduled:
            batch.append(UpdateOne(
                {
                    'type': 'panel',
                    'survey_id': spm.get('SurveyJsId__c'),
                    'contact_id': contact.get('Id'),
                    'template_name': email_template_name,
                    'sent_to_sendgrid_date': {'$exists': False},
                },
                {
                    '$set': {
                        'type': 'panel',
                        'survey_id': spm.get('SurveyJsId__c'),
                        'contact_id': contact.get('Id'),
                        'survey_panel_member_id': spm.get('Id'),
                        'survey_client_id': spm.get('Survey_Client__r.Id'),
                        'customer_survey_id': spm.get('Survey_Client__r.Customer_Survey__r.Id'),
                        'customer_survey_round_id': csr_id,
                        'survey_round_id': spm.get('Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Survey_Round__r.Id'),
                        'sender_email': sender,
                        'sender_name': from_name,
                        'recipient': recipient,
                        'template_name': email_template_name,
                        'template_data': template_data,
                        'template_metadata': template_metadata,
                        'language': locale,
                        'vertical': vertical,
                        'banner_attachment': banner_attachment,
                        'signature_attachment': signature_attachment,
                        'scheduled_date_local': scheduled_date_local.isoformat(),
                        'scheduled_date_utc': scheduled_date_utc,
                        'scheduled_date_timezone': timezone,
                        'sf_spm_field': spm_property_name,
                        'last_modified': now,
                        'email_service_provider': email_service_provider,
                    },
                    '$addToSet': {
                        'survey_panel_member_ids': spm.get('Id'),
                        'survey_client_ids': spm.get('Survey_Client__r.Id'),
                        'customer_survey_ids': spm.get('Survey_Client__r.Customer_Survey__r.Id'),
                    }
                },
                upsert=True
            ))
            scheduled_count += 1
            print(f'   :: Email Scheduled: {email_template_name} ({locale}) to {recipient} at {scheduled_date_utc} for SPM {spm.get("Id")} | SC: {spm.get('Survey_Client__r.Id')} |  CS: {spm.get('Survey_Client__r.Customer_Survey__r.Id')}')
        else:
            batch.append(UpdateOne(
                {
                    'type': 'panel',
                    'survey_id': spm.get('SurveyJsId__c'),
                    'contact_id': contact.get('Id'),
                    'template_name': email_template_name,
                    'sent_to_sendgrid_date': {'$exists': False},
                },
                {
                    '$set': {
                        'last_modified': now,
                    },
                    '$addToSet': {
                        'survey_panel_member_ids': spm.get('Id'),
                        'survey_client_ids': spm.get('Survey_Client__r.Id'),
                        'customer_survey_ids': spm.get('Survey_Client__r.Customer_Survey__r.Id'),
                    }
                }
            ))
            print(f'   :: Email Skipped: {email_template_name} ({locale}) already scheduled for {recipient} in this run for SPM {spm.get("Id")} | SC: {spm.get('Survey_Client__r.Id')} |  CS: {spm.get('Survey_Client__r.Customer_Survey__r.Id')}')

        # record this contact as having been scheduled for this survey
        has_been_scheduled.add(has_been_schduled_key)

    # if we had emails scheduled to be sent in the portaldb, but they were not in the SF data, delete them
    # as we can assume those schedules are no longer valid
    for processed_template_name in templates_processed:
        for key in scheduled_emails.get(processed_template_name, {}).get('active', set()):
            if key not in has_been_scheduled:
                contact_id, survey_id, template_name = key.split('-')
                batch.append(UpdateOne(
                    {
                        'type': 'panel',
                        'survey_id': survey_id,
                        'contact_id': contact_id,
                        'template_name': template_name,
                        'sent_to_sendgrid_date': {'$exists': False},
                        'deleted': {'$exists': False},
                    },
                    {
                        '$set': {
                            'deleted': now,
                            'last_modified': now,
                        }
                    }
                ))

    # if we had emails scheduled to be sent in the portaldb but marked as deleted, but they were in the SF data, "undelete" them
    # as we can assume those schedules are valid again
    for processed_template_name in templates_processed:
        for key in scheduled_emails.get(processed_template_name, {}).get('deleted', set()):
            if key in has_been_scheduled:
                contact_id, survey_id, template_name = key.split('-')
                batch.append(UpdateOne(
                    {
                        'type': 'panel',
                        'survey_id': survey_id,
                        'contact_id': contact_id,
                        'template_name': template_name,
                        'sent_to_sendgrid_date': {'$exists': False},
                        'deleted': {'$exists': True},
                    },
                    {
                        '$set': {
                            'last_modified': now,
                        },
                        '$unset': {
                            'deleted': '',
                        }
                    }
                ))

    print(f'   :: Emails Scheduled: {scheduled_count}')

    return batch


def main(customer_survey_round_id:str|None = None, writeit:bool = False):
    sf:Salesforce = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    db:portaldbapi.DocumentDB = portaldbapi.DocumentDB()
    collection:portaldbapi.DocumentDB = portaldbapi.get_sf_collection(db, portaldbapi.PORTALDB_SCHEDULED_EMAILS_COLLECTION)

    print('START: EMAIL SCHEDULER PANEL USERS')

    batch:list[UpdateOne] = []
    account_record_types:dict[str,dict] = {x.Name: x for x in sfapi.get_all(sf, sfapi.AccountRecordType)}

    # get all customer survey rounds for the current round
    customer_survey_rounds:list[dict] = get_customer_survey_rounds_from_sf(sf, customer_survey_round_id)
    customer_survey_round_ids:list[str] = [csr.get('Id') for csr in customer_survey_rounds]

    # get all survey panel members for the customer survey rounds in the current round
    survey_panel_members:dict[str, list] = get_panel_members_from_sf(sf, customer_survey_round_ids)

    # get both the currently scheduled and already sent emails for the customer survey rounds
    # in the current round
    scheduled_emails:dict[str, dict] = get_scheduled_emails_from_portaldb(collection, customer_survey_round_ids)

    for csr in customer_survey_rounds:
        csr_id:str = csr.get('Id')
        cs_name:str = csr.get('Name')

        print(f' # START: Customer Survey Round: {cs_name} ({csr_id})')

        grouped_panel_members:dict = group_survey_panel_members(survey_panel_members.get(csr_id, []))
        contact_profile_count:dict[str,set] = grouped_panel_members.get('contact_profile_count')
        contact_survey_names:dict[str,set] = grouped_panel_members.get('contact_survey_names')
        csr_scheduled_emails:dict[str, set] = scheduled_emails.get(csr_id, {})

        print('  >> PROCESSING: M1...')
        # process M1 participants
        batch.extend(process_participants(csr_id,
                                          grouped_panel_members['first_email_panel_members'],
                                          contact_profile_count,
                                          contact_survey_names,
                                          0,
                                          account_record_types,
                                          csr_scheduled_emails))
        print('  >> PROCESSING: M2...')
        # process M2 participants
        batch.extend(process_participants(csr_id,
                                          grouped_panel_members['reminder_1_email_panel_members'],
                                          contact_profile_count,
                                          contact_survey_names,
                                          1,
                                          account_record_types,
                                          csr_scheduled_emails))
        print('  >> PROCESSING: M3...')
        # process M3 participants
        batch.extend(process_participants(csr_id,
                                          grouped_panel_members['reminder_2_email_panel_members'],
                                          contact_profile_count,
                                          contact_survey_names,
                                          2,
                                          account_record_types,
                                          csr_scheduled_emails))
        print('  >> PROCESSING: M4...')
        # process M4 participants
        batch.extend(process_participants(csr_id,
                                          grouped_panel_members['reminder_3_email_panel_members'],
                                          contact_profile_count,
                                          contact_survey_names,
                                          3,
                                          account_record_types,
                                          csr_scheduled_emails))

        print(f' # END: Customer Survey Round: {cs_name} ({csr_id})')

    if writeit and batch:
        print(' # UPDATING PORTAL DB...')
        collection.bulk_write(batch)

    print(f'END: EMAIL SCHEDULER PANEL USERS')


def lambda_handler(event, context):
    main(
        writeit=True
    )


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument('--customer_survey_round_id', default=None, help='single survey round to run for')
    ap.add_argument('--writeit', action='store_true', help='Actually write data to portaldb')
    args = ap.parse_args()

    main(customer_survey_round_id=args.customer_survey_round_id, writeit=args.writeit)
