#!/bin/sh

set -e

XLSX=$1
IMPORTTYPE=$2
WRITEIT=$3
XLSXBASE=`basename "$XLSX"`

NOSAFEEMAILS=""
if [ "$STACK" = "prod" ]; then
    NOSAFEEMAILS="--nosafeemails"
    read -p "Are you sure you mean to run against prod? (type YES if you are) " PROD_IS_OK
    if [ "$PROD_IS_OK" != "YES" ]; then
        echo "Aborting"
        exit 1
    fi
    export PROD_IS_OK

    if [ "$AGENCY_DEV_PREFIX" != "" ]; then
        echo "AGENCY_DEV_PREFIX must not be set in prod"
        exit 1
    fi
fi

python3 -m devtools.xlsxtocsv "$XLSX"
python3 -m sftools.importsurveycsv "$XLSX.sheet1.csv" $NOSAFEEMAILS $IMPORTTYPE $WRITEIT
